// Performance Enhancements for Image Converter

// Lazy Loading Implementation
class LazyLoader {
    constructor() {
        this.imageObserver = null;
        this.init();
    }

    init() {
        if ('IntersectionObserver' in window) {
            this.imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        observer.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                this.imageObserver.observe(img);
            });
        }
    }
}

// Preload Critical Resources
class ResourcePreloader {
    constructor() {
        this.preloadCriticalResources();
    }

    preloadCriticalResources() {
        // Preload critical CSS
        this.preloadCSS('/css/app.css');
        
        // Preload critical fonts
        this.preloadFont('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    }

    preloadCSS(href) {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'style';
        link.href = href;
        link.onload = () => {
            link.rel = 'stylesheet';
        };
        document.head.appendChild(link);
    }

    preloadFont(href) {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'style';
        link.href = href;
        document.head.appendChild(link);
    }
}

// Performance Monitoring
class PerformanceMonitor {
    constructor() {
        this.metrics = {};
        this.init();
    }

    init() {
        // Monitor Core Web Vitals
        this.measureCLS();
        this.measureFID();
        this.measureLCP();
    }

    measureCLS() {
        if ('LayoutShift' in window) {
            new PerformanceObserver((list) => {
                let clsValue = 0;
                for (const entry of list.getEntries()) {
                    if (!entry.hadRecentInput) {
                        clsValue += entry.value;
                    }
                }
                this.metrics.cls = clsValue;
            }).observe({ type: 'layout-shift', buffered: true });
        }
    }

    measureFID() {
        if ('PerformanceEventTiming' in window) {
            new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    this.metrics.fid = entry.processingStart - entry.startTime;
                    break;
                }
            }).observe({ type: 'first-input', buffered: true });
        }
    }

    measureLCP() {
        if ('LargestContentfulPaint' in window) {
            new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const lastEntry = entries[entries.length - 1];
                this.metrics.lcp = lastEntry.startTime;
            }).observe({ type: 'largest-contentful-paint', buffered: true });
        }
    }

    getMetrics() {
        return this.metrics;
    }
}

// Image Optimization
class ImageOptimizer {
    constructor() {
        this.init();
    }

    init() {
        this.optimizeImages();
        this.addWebPSupport();
    }

    optimizeImages() {
        document.querySelectorAll('img').forEach(img => {
            // Add loading="lazy" for better performance
            if (!img.hasAttribute('loading')) {
                img.setAttribute('loading', 'lazy');
            }

            // Add proper alt text if missing
            if (!img.hasAttribute('alt')) {
                img.setAttribute('alt', '');
            }
        });
    }

    addWebPSupport() {
        // Check WebP support
        const webpSupported = this.supportsWebP();
        if (webpSupported) {
            document.documentElement.classList.add('webp-supported');
        }
    }

    supportsWebP() {
        const canvas = document.createElement('canvas');
        canvas.width = 1;
        canvas.height = 1;
        return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
    }
}

// Smooth Scrolling Enhancement
class SmoothScroller {
    constructor() {
        this.init();
    }

    init() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(anchor.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }
}

// Service Worker Registration
class ServiceWorkerManager {
    constructor() {
        this.init();
    }

    init() {
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    }
}

// Critical CSS Inlining
class CriticalCSSManager {
    constructor() {
        this.init();
    }

    init() {
        // Load non-critical CSS asynchronously
        this.loadNonCriticalCSS();
    }

    loadNonCriticalCSS() {
        const nonCriticalCSS = [
            '/css/enhanced-styles.css',
            '/css/share-buttons.css'
        ];

        nonCriticalCSS.forEach(href => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            link.media = 'print';
            link.onload = () => {
                link.media = 'all';
            };
            document.head.appendChild(link);
        });
    }
}

// Initialize all performance enhancements
document.addEventListener('DOMContentLoaded', () => {
    new LazyLoader();
    new ResourcePreloader();
    new PerformanceMonitor();
    new ImageOptimizer();
    new SmoothScroller();
    new ServiceWorkerManager();
    new CriticalCSSManager();
});

// Enhanced File Upload Experience
class FileUploadEnhancer {
    constructor() {
        this.init();
    }

    init() {
        this.enhanceFilePond();
        this.addProgressIndicators();
        this.addDragDropEffects();
    }

    enhanceFilePond() {
        // Add custom styling and behavior to FilePond
        document.addEventListener('FilePond:loaded', () => {
            const pondElements = document.querySelectorAll('.filepond--root');
            pondElements.forEach(pond => {
                pond.addEventListener('dragenter', this.handleDragEnter);
                pond.addEventListener('dragleave', this.handleDragLeave);
                pond.addEventListener('drop', this.handleDrop);
            });
        });
    }

    handleDragEnter(e) {
        e.currentTarget.classList.add('drag-over');
    }

    handleDragLeave(e) {
        e.currentTarget.classList.remove('drag-over');
    }

    handleDrop(e) {
        e.currentTarget.classList.remove('drag-over');
        this.showUploadProgress();
    }

    addProgressIndicators() {
        // Add visual progress indicators
        const style = document.createElement('style');
        style.textContent = `
            .drag-over {
                border-color: #3b82f6 !important;
                background-color: #eff6ff !important;
                transform: scale(1.02);
            }
            .upload-progress {
                position: relative;
                overflow: hidden;
            }
            .upload-progress::after {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
                animation: shimmer 2s infinite;
            }
            @keyframes shimmer {
                0% { left: -100%; }
                100% { left: 100%; }
            }
        `;
        document.head.appendChild(style);
    }

    addDragDropEffects() {
        // Prevent default drag behaviors on document
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            document.addEventListener(eventName, (e) => {
                e.preventDefault();
                e.stopPropagation();
            });
        });
    }

    showUploadProgress() {
        const uploadArea = document.querySelector('.filepond--root');
        if (uploadArea) {
            uploadArea.classList.add('upload-progress');
            setTimeout(() => {
                uploadArea.classList.remove('upload-progress');
            }, 3000);
        }
    }
}

// User Experience Enhancements
class UXEnhancer {
    constructor() {
        this.init();
    }

    init() {
        this.addTooltips();
        this.enhanceButtons();
        this.addKeyboardShortcuts();
        this.improveAccessibility();
    }

    addTooltips() {
        // Add helpful tooltips
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', this.showTooltip);
            element.addEventListener('mouseleave', this.hideTooltip);
        });
    }

    showTooltip(e) {
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = e.target.dataset.tooltip;
        tooltip.style.cssText = `
            position: absolute;
            background: #1f2937;
            color: white;
            padding: 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            z-index: 1000;
            pointer-events: none;
        `;
        document.body.appendChild(tooltip);

        const rect = e.target.getBoundingClientRect();
        tooltip.style.left = rect.left + 'px';
        tooltip.style.top = (rect.top - tooltip.offsetHeight - 5) + 'px';
    }

    hideTooltip() {
        const tooltip = document.querySelector('.tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    }

    enhanceButtons() {
        // Add ripple effect to buttons
        const buttons = document.querySelectorAll('button, .btn');
        buttons.forEach(button => {
            button.addEventListener('click', this.createRipple);
        });
    }

    createRipple(e) {
        const button = e.currentTarget;
        const ripple = document.createElement('span');
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;

        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        `;

        button.style.position = 'relative';
        button.style.overflow = 'hidden';
        button.appendChild(ripple);

        setTimeout(() => ripple.remove(), 600);
    }

    addKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + U for upload
            if ((e.ctrlKey || e.metaKey) && e.key === 'u') {
                e.preventDefault();
                const uploadButton = document.querySelector('.filepond--browser');
                if (uploadButton) uploadButton.click();
            }

            // Escape to close modals
            if (e.key === 'Escape') {
                const modals = document.querySelectorAll('.modal[open]');
                modals.forEach(modal => modal.close());
            }
        });
    }

    improveAccessibility() {
        // Add ARIA labels and improve focus management
        const images = document.querySelectorAll('img:not([alt])');
        images.forEach(img => {
            img.setAttribute('alt', '');
        });

        // Improve focus visibility
        const style = document.createElement('style');
        style.textContent = `
            *:focus {
                outline: 2px solid #3b82f6;
                outline-offset: 2px;
            }
            .sr-only {
                position: absolute;
                width: 1px;
                height: 1px;
                padding: 0;
                margin: -1px;
                overflow: hidden;
                clip: rect(0, 0, 0, 0);
                white-space: nowrap;
                border: 0;
            }
        `;
        document.head.appendChild(style);
    }
}

// Initialize all enhancements
document.addEventListener('DOMContentLoaded', () => {
    new LazyLoader();
    new ResourcePreloader();
    new PerformanceMonitor();
    new ImageOptimizer();
    new SmoothScroller();
    new ServiceWorkerManager();
    new CriticalCSSManager();
    new FileUploadEnhancer();
    new UXEnhancer();
});

// Export for use in other modules
window.PerformanceEnhancements = {
    LazyLoader,
    ResourcePreloader,
    PerformanceMonitor,
    ImageOptimizer,
    SmoothScroller,
    ServiceWorkerManager,
    CriticalCSSManager,
    FileUploadEnhancer,
    UXEnhancer
};
