<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- SEO Meta Tags -->
    @if ($noIndex)
        <meta name="robots" content="noindex,nofollow">
    @else
        <meta name="robots" content="index,follow">
    @endif

    <!-- Performance Hints -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//fonts.gstatic.com">
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Favicon -->
    <link rel="shortcut icon" href="{{ asset('/storage/favicon.ico') }}" type="image/x-icon">
    <link rel="icon" type="image/x-icon" href="{{ asset('/storage/favicon.ico') }}">

    <!-- SEO Meta -->
    {!! SEOMeta::generate() !!}

    <!-- Critical CSS - Load fonts with display=swap for better performance -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome - Load asynchronously -->
    <link rel="preload" href="//cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css"></noscript>

    <!-- Application Assets -->
    @vite(['resources/css/app.css', 'resources/css/share-buttons.css', 'resources/js/app.js'])
    @livewireStyles
    @if (!empty($generalSettings->headerTags))
        {!! $generalSettings->headerTags !!}
    @endif
    @if (!empty($generalSettings->analyticsId))
        <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id={{ $generalSettings->analyticsId }}"></script>
        <script>
            window.dataLayer = window.dataLayer || [];

            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());

            gtag('config', '{{ $generalSettings->analyticsId }}');
        </script>
    @endif
    <!-- Critical CSS -->
    <style>
        /* Alpine.js cloak */
        [x-cloak] {
            display: none !important;
        }

        /* CSS Custom Properties */
        :root {
            --bg-color: {{ $styleSettings->bgColor }};
            --text-color: {{ $styleSettings->textColor }};
            --nav-bg-color: {{ $styleSettings->navBgColor }};
            --nav-text-color: {{ $styleSettings->navTextColor }};
            --uploader-bg-color: {{ $styleSettings->uploaderBgColor }};
            --uploader-text-color: {{ $styleSettings->uploaderTextColor }};
            --uploader-bar-text-color: {{ $styleSettings->uploadBarTextColor }};
            --uploader-bar-success-bg-color: {{ $styleSettings->uploadBarSuccessBgColor }};
            --uploader-bar-error-bg-color: {{ $styleSettings->uploadBarErrorBgColor }};
            --browse-btn-bg-color: {{ $styleSettings->browseBtnBgColor }};
            --browse-btn-text-color: {{ $styleSettings->browseBtnTextColor }};
            --convert-btn-bg-color: {{ $styleSettings->convertBtnBgColor }};
            --convert-btn-text-color: {{ $styleSettings->convertBtnTextColor }};
            --card-bg-color: {{ $styleSettings->cardBgColor }};
            --card-text-color: {{ $styleSettings->cardTextColor }};
            --card-hover-bg-color: {{ $styleSettings->cardHoverBgColor }};
            --card-hover-text-color: {{ $styleSettings->cardHoverTextColor }};

            /* Performance optimizations */
            --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* Critical styles for above-the-fold content */
        body {
            font-family: var(--font-family);
            background-color: var(--bg-color);
            color: var(--text-color);
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Loading skeleton animation */
        .skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        /* Custom CSS from settings */
        {{ $styleSettings->customCSS }}
    </style>

    <!-- Structured Data for SEO -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "{{ $generalSettings->siteName ?? 'Image Converter' }}",
        "description": "{{ $generalSettings->siteDescription ?? 'Convert images between different formats online' }}",
        "url": "{{ url('/') }}",
        "applicationCategory": "UtilityApplication",
        "operatingSystem": "Any",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "featureList": [
            "Image format conversion",
            "Batch processing",
            "Multiple format support",
            "Free online tool"
        ]
    }
    </script>
</head>
