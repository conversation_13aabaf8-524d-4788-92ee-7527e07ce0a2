<x-layouts.app>
    <div id="home-page" class="space-y-8">
        <!-- Hero Section -->
        <header class="text-center py-12 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl shadow-sm">
            <div class="max-w-4xl mx-auto px-6">
                <h1 class="mb-6 text-4xl md:text-5xl font-bold text-gray-900 leading-tight">
                    {{ $settings->headerTitle }}
                </h1>
                <p class="text-xl text-gray-600 mb-8 leading-relaxed">
                    {{ $settings->headerSubtitle }}
                </p>

                <!-- Quick Stats -->
                <div class="flex flex-wrap justify-center gap-6 text-sm text-gray-500">
                    <div class="flex items-center gap-2">
                        <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>Free & Secure</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                        </svg>
                        <span>25+ Formats</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <svg class="w-5 h-5 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"></path>
                        </svg>
                        <span>Lightning Fast</span>
                    </div>
                </div>
            </div>
        </header>

        @if ($settings->topAd)
            <div class="mb-8 top-ad">
                {!! $adSettings->topAdCode !!}
            </div>
        @endif
        @php
            $items = collect(converters())
                ->map(fn($converter) => $converter['enabled'])
                ->toArray();
            $showConverters = in_array(true, $items);
        @endphp
        @if ($showConverters)
            <!-- Converters Section -->
            <section class="mb-12">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">Choose Your Converter</h2>
                    <p class="text-gray-600 max-w-2xl mx-auto">
                        Select from our wide range of image format converters. All conversions are processed securely on our servers.
                    </p>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    @foreach (converters() as $converter)
                        @if ($converter['enabled'])
                            <a class="group relative bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-lg hover:border-blue-200 transition-all duration-300 transform hover:-translate-y-1"
                                href="{{ localizedRoute('resolver', $slugs->{$converter['name']}) }}"
                                aria-label="Convert to {{ $converter['format'] }} format">

                                <!-- Views Badge -->
                                @if ($converter['views'])
                                    <div class="absolute top-3 right-3 bg-blue-50 text-blue-600 text-xs px-2 py-1 rounded-full flex items-center gap-1">
                                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        {{ number_format($converter['views']) }}
                                    </div>
                                @endif

                                <!-- Icon -->
                                <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-blue-50 to-indigo-100 group-hover:from-blue-100 group-hover:to-indigo-200 transition-colors duration-300">
                                    <x-dynamic-component :component="$converter['icon']" class="w-8 h-8 text-blue-600" />
                                </div>

                                <!-- Content -->
                                <div class="text-center">
                                    <h3 class="font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-300">
                                        {{ $converter['entryTitle'] }}
                                    </h3>
                                    <p class="text-sm text-gray-500 leading-relaxed">
                                        {{ $converter['entrySummary'] }}
                                    </p>
                                </div>

                                <!-- Hover Effect -->
                                <div class="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500 to-indigo-600 opacity-0 group-hover:opacity-5 transition-opacity duration-300"></div>
                            </a>
                        @endif
                    @endforeach
                </div>
            </section>
            @if ($settings->content)
                @if ($settings->middleAd)
                    <div class="mb-8 middle-ad">
                        {!! $adSettings->middleAdCode !!}
                    </div>
                @endif

                <!-- About Section -->
                <section class="mb-12 bg-gray-50 rounded-2xl p-8">
                    <div class="max-w-4xl mx-auto">
                        <div class="prose prose-lg max-w-none text-gray-700">
                            {!! $settings->content !!}
                        </div>
                    </div>
                </section>
            @endif

            <!-- Features Section -->
            <section class="mb-12">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">Why Choose Our Converter?</h2>
                    <p class="text-gray-600 max-w-2xl mx-auto">
                        Experience the best online image conversion tool with advanced features and lightning-fast processing.
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <div class="text-center p-6">
                        <div class="w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">100% Secure</h3>
                        <p class="text-gray-600">Your files are processed securely and automatically deleted after conversion.</p>
                    </div>

                    <div class="text-center p-6">
                        <div class="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Lightning Fast</h3>
                        <p class="text-gray-600">Advanced processing algorithms ensure quick conversion without quality loss.</p>
                    </div>

                    <div class="text-center p-6">
                        <div class="w-16 h-16 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Batch Processing</h3>
                        <p class="text-gray-600">Convert multiple images at once and download them as a convenient ZIP file.</p>
                    </div>
                </div>
            </section>

            @if ($settings->bottomAd)
                <div class="mb-8 bottom-ad">
                    {!! $adSettings->bottomAdCode !!}
                </div>
            @endif

            @if ($settings->showShareButtons)
                <div class="text-center">
                    <x-share-buttons />
                </div>
            @endif
        @else
            <!-- No Converters Available -->
            <section class="text-center py-16">
                <div class="max-w-md mx-auto">
                    <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-12 h-12 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">{{ __('No Converters Available') }}</h3>
                    <p class="text-gray-600">{{ __('Please contact the administrator to enable converters.') }}</p>
                </div>
            </section>
        @endif
    </div>
</x-layouts.app>
