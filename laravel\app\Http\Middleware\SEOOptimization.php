<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class SEOOptimization
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        // Only apply to HTML responses
        if ($response instanceof Response && 
            str_contains($response->headers->get('Content-Type', ''), 'text/html')) {
            
            $content = $response->getContent();
            
            // Apply SEO optimizations
            $content = $this->optimizeHTML($content);
            $content = $this->addStructuredData($content, $request);
            $content = $this->optimizeImages($content);
            $content = $this->addPreloadHints($content);
            
            $response->setContent($content);
            
            // Add performance headers
            $this->addPerformanceHeaders($response);
        }

        return $response;
    }

    /**
     * Optimize HTML structure for SEO
     */
    private function optimizeHTML(string $content): string
    {
        // Minify HTML (remove unnecessary whitespace)
        $content = preg_replace('/\s+/', ' ', $content);
        $content = preg_replace('/>\s+</', '><', $content);
        
        // Add missing alt attributes to images
        $content = preg_replace('/<img(?![^>]*alt=)([^>]*)>/', '<img$1 alt="">', $content);
        
        // Ensure all links have proper attributes
        $content = preg_replace('/<a\s+href="(https?:\/\/[^"]*)"(?![^>]*rel=)([^>]*)>/', '<a href="$1" rel="noopener"$2>', $content);
        
        return $content;
    }

    /**
     * Add structured data for better SEO
     */
    private function addStructuredData(string $content, Request $request): string
    {
        $structuredData = [
            '@context' => 'https://schema.org',
            '@type' => 'WebApplication',
            'name' => config('app.name', 'Image Converter'),
            'description' => 'Convert images between different formats online for free',
            'url' => $request->url(),
            'applicationCategory' => 'UtilityApplication',
            'operatingSystem' => 'Any',
            'offers' => [
                '@type' => 'Offer',
                'price' => '0',
                'priceCurrency' => 'USD'
            ],
            'featureList' => [
                'Image format conversion',
                'Batch processing',
                'Multiple format support',
                'Free online tool'
            ]
        ];

        // Add breadcrumb data if applicable
        if ($request->path() !== '/') {
            $breadcrumbs = $this->generateBreadcrumbs($request);
            if (!empty($breadcrumbs)) {
                $structuredData['breadcrumb'] = $breadcrumbs;
            }
        }

        $jsonLd = '<script type="application/ld+json">' . json_encode($structuredData, JSON_UNESCAPED_SLASHES) . '</script>';
        
        // Insert before closing head tag
        $content = str_replace('</head>', $jsonLd . '</head>', $content);
        
        return $content;
    }

    /**
     * Generate breadcrumb structured data
     */
    private function generateBreadcrumbs(Request $request): array
    {
        $segments = explode('/', trim($request->path(), '/'));
        $breadcrumbs = [
            '@type' => 'BreadcrumbList',
            'itemListElement' => []
        ];

        $url = $request->getSchemeAndHttpHost();
        $breadcrumbs['itemListElement'][] = [
            '@type' => 'ListItem',
            'position' => 1,
            'name' => 'Home',
            'item' => $url
        ];

        $position = 2;
        foreach ($segments as $segment) {
            if (!empty($segment)) {
                $url .= '/' . $segment;
                $breadcrumbs['itemListElement'][] = [
                    '@type' => 'ListItem',
                    'position' => $position++,
                    'name' => ucfirst(str_replace('-', ' ', $segment)),
                    'item' => $url
                ];
            }
        }

        return $breadcrumbs;
    }

    /**
     * Optimize images for better performance
     */
    private function optimizeImages(string $content): string
    {
        // Add loading="lazy" to images that don't have it
        $content = preg_replace('/<img(?![^>]*loading=)([^>]*)>/', '<img loading="lazy"$1>', $content);
        
        // Add decoding="async" for better performance
        $content = preg_replace('/<img(?![^>]*decoding=)([^>]*)>/', '<img decoding="async"$1>', $content);
        
        return $content;
    }

    /**
     * Add preload hints for critical resources
     */
    private function addPreloadHints(string $content): string
    {
        $preloadHints = [
            '<link rel="preload" href="/css/app.css" as="style">',
            '<link rel="preload" href="/js/app.js" as="script">',
            '<link rel="dns-prefetch" href="//fonts.googleapis.com">',
            '<link rel="dns-prefetch" href="//cdnjs.cloudflare.com">',
        ];

        $hintsHtml = implode("\n", $preloadHints);
        $content = str_replace('</head>', $hintsHtml . "\n</head>", $content);
        
        return $content;
    }

    /**
     * Add performance-related headers
     */
    private function addPerformanceHeaders(Response $response): void
    {
        // Cache control
        $response->headers->set('Cache-Control', 'public, max-age=3600');
        
        // Security headers
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-Frame-Options', 'DENY');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');
        
        // Performance headers
        $response->headers->set('X-DNS-Prefetch-Control', 'on');
        
        // Compression
        if (!$response->headers->has('Content-Encoding')) {
            $response->headers->set('Vary', 'Accept-Encoding');
        }
    }
}
