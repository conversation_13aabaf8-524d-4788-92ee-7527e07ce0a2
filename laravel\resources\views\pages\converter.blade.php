<x-layouts.app>
    <div id="converter" class="space-y-8">
        <!-- Header Section -->
        <header class="text-center py-8 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl shadow-sm">
            <div class="max-w-4xl mx-auto px-6">
                <div class="flex items-center justify-center mb-4">
                    <div class="w-16 h-16 bg-white rounded-2xl shadow-sm flex items-center justify-center mr-4">
                        <span class="text-2xl font-bold text-blue-600 uppercase">{{ strtoupper($format) }}</span>
                    </div>
                    <div class="text-left">
                        <h1 class="text-3xl md:text-4xl font-bold text-gray-900 leading-tight">
                            {{ $headerTitle }}
                        </h1>
                        <p class="text-lg text-gray-600 mt-2">
                            {{ $headerSubtitle }}
                        </p>
                    </div>
                </div>

                <!-- Format Info -->
                <div class="mt-6 flex flex-wrap justify-center gap-4 text-sm text-gray-500">
                    <div class="flex items-center gap-2">
                        <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>High Quality</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <svg class="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"></path>
                        </svg>
                        <span>Fast Processing</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <svg class="w-4 h-4 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span>Secure & Private</span>
                    </div>
                </div>
            </div>
        </header>

        @if ($showTopAd)
            <div class="mb-8 top-ad">
                {!! $adSettings->topAdCode !!}
            </div>
        @endif

        <!-- Uploader Section -->
        <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
            <livewire:uploader :slug="$slug" :format="$format" />
        </div>

        @if ($showMiddleAd)
            <div class="mb-8 middle-ad">
                {!! $adSettings->middleAdCode !!}
            </div>
        @endif

        <!-- Description Section -->
        @if ($description)
            <section class="bg-gray-50 rounded-2xl p-8">
                <div class="max-w-4xl mx-auto">
                    <div class="prose prose-lg max-w-none text-gray-700">
                        {!! $description !!}
                    </div>
                </div>
            </section>
        @endif
        @php
            $converters = collect(converters())
                ->filter(fn($converter) => $converter['format'] !== $format && $converter['enabled'])
                ->take(12)
                ->toArray();
        @endphp

        <!-- Related Converters Section -->
        @if (!empty($converters))
            <section class="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
                <div class="text-center mb-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">Other Popular Converters</h2>
                    <p class="text-gray-600">
                        Explore more image format conversion tools
                    </p>
                </div>

                <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4">
                    @foreach ($converters as $converter)
                        <a class="group p-4 rounded-xl border border-gray-100 hover:border-blue-200 hover:shadow-md transition-all duration-300 text-center"
                            href="{{ localizedRoute('resolver', $slugs->{$converter['name']}) }}"
                            aria-label="Convert to {{ $converter['format'] }} format">

                            <div class="w-12 h-12 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:from-blue-100 group-hover:to-indigo-200 transition-colors duration-300">
                                <x-dynamic-component :component="$converter['icon']" class="w-6 h-6 text-blue-600" />
                            </div>

                            <h3 class="font-semibold text-gray-900 text-sm group-hover:text-blue-600 transition-colors duration-300">
                                {{ $converter['title'] }}
                            </h3>

                            @if ($converter['views'])
                                <p class="text-xs text-gray-500 mt-1">
                                    {{ number_format($converter['views']) }} uses
                                </p>
                            @endif
                        </a>
                    @endforeach
                </div>

                <div class="text-center mt-8">
                    <a href="{{ localizedRoute('home') }}"
                       class="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-xl hover:bg-blue-700 transition-colors duration-300">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        View All Converters
                    </a>
                </div>
            </section>
        @endif

        @if ($showBottomAd)
            <div class="mb-8 bottom-ad">
                {!! $adSettings->bottomAdCode !!}
            </div>
        @endif

        @if ($showShareButtons)
            <div class="text-center">
                <x-share-buttons />
            </div>
        @endif
    </div>
</x-layouts.app>
