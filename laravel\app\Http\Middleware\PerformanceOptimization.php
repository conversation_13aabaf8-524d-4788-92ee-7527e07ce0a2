<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class PerformanceOptimization
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        // Only apply to HTML responses
        if ($response instanceof Response && 
            str_contains($response->headers->get('Content-Type', ''), 'text/html')) {
            
            $content = $response->getContent();
            
            // Apply performance optimizations
            $content = $this->optimizeCSS($content);
            $content = $this->optimizeJavaScript($content);
            $content = $this->addResourceHints($content);
            $content = $this->optimizeImages($content);
            
            $response->setContent($content);
            
            // Add performance headers
            $this->addPerformanceHeaders($response);
        }

        return $response;
    }

    /**
     * Optimize CSS loading
     */
    private function optimizeCSS(string $content): string
    {
        // Move non-critical CSS to load asynchronously
        $nonCriticalCSS = [
            '/css/share-buttons.css',
            'font-awesome',
            'cdnjs.cloudflare.com'
        ];

        foreach ($nonCriticalCSS as $css) {
            $pattern = '/<link[^>]*href="[^"]*' . preg_quote($css, '/') . '[^"]*"[^>]*>/';
            $content = preg_replace_callback($pattern, function($matches) {
                $link = $matches[0];
                // Convert to async loading
                if (!str_contains($link, 'media=')) {
                    $link = str_replace('<link', '<link media="print" onload="this.media=\'all\'"', $link);
                    $link .= '<noscript>' . str_replace('media="print" onload="this.media=\'all\'"', '', $link) . '</noscript>';
                }
                return $link;
            }, $content);
        }

        return $content;
    }

    /**
     * Optimize JavaScript loading
     */
    private function optimizeJavaScript(string $content): string
    {
        // Add defer to non-critical scripts
        $content = preg_replace(
            '/<script(?![^>]*(?:defer|async))([^>]*src="[^"]*")([^>]*)>/i',
            '<script defer$1$2>',
            $content
        );

        return $content;
    }

    /**
     * Add resource hints for better performance
     */
    private function addResourceHints(string $content): string
    {
        $hints = [
            // DNS prefetch for external domains
            '<link rel="dns-prefetch" href="//fonts.googleapis.com">',
            '<link rel="dns-prefetch" href="//fonts.gstatic.com">',
            '<link rel="dns-prefetch" href="//cdnjs.cloudflare.com">',
            
            // Preconnect to critical domains
            '<link rel="preconnect" href="https://fonts.googleapis.com">',
            '<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>',
            
            // Preload critical resources
            '<link rel="preload" href="/css/app.css" as="style">',
            '<link rel="preload" href="/js/app.js" as="script">',
        ];

        $hintsHtml = implode("\n    ", $hints);
        $content = str_replace('</head>', "    {$hintsHtml}\n</head>", $content);

        return $content;
    }

    /**
     * Optimize images for better performance
     */
    private function optimizeImages(string $content): string
    {
        // Add loading="lazy" to images
        $content = preg_replace(
            '/<img(?![^>]*loading=)([^>]*)>/i',
            '<img loading="lazy"$1>',
            $content
        );

        // Add decoding="async" for better performance
        $content = preg_replace(
            '/<img(?![^>]*decoding=)([^>]*)>/i',
            '<img decoding="async"$1>',
            $content
        );

        // Add proper dimensions if missing (prevents layout shift)
        $content = preg_replace_callback(
            '/<img([^>]*)>/i',
            function($matches) {
                $img = $matches[0];
                if (!str_contains($img, 'width=') && !str_contains($img, 'height=')) {
                    // Add default dimensions to prevent layout shift
                    $img = str_replace('<img', '<img width="auto" height="auto"', $img);
                }
                return $img;
            },
            $content
        );

        return $content;
    }

    /**
     * Add performance-related headers
     */
    private function addPerformanceHeaders(Response $response): void
    {
        // Cache control for static assets
        if ($this->isStaticAsset(request())) {
            $response->headers->set('Cache-Control', 'public, max-age=31536000, immutable');
        } else {
            $response->headers->set('Cache-Control', 'public, max-age=3600');
        }

        // Compression
        $response->headers->set('Vary', 'Accept-Encoding');

        // Performance hints
        $response->headers->set('X-DNS-Prefetch-Control', 'on');

        // Security headers that also improve performance
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-Frame-Options', 'DENY');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');

        // Add Content Security Policy for better security and performance
        $csp = "default-src 'self'; " .
               "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdnjs.cloudflare.com; " .
               "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; " .
               "font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; " .
               "img-src 'self' data: https:; " .
               "connect-src 'self';";
        
        $response->headers->set('Content-Security-Policy', $csp);

        // Add Early Hints support (HTTP/2 Server Push alternative)
        if (function_exists('header') && !headers_sent()) {
            header('Link: </css/app.css>; rel=preload; as=style', false);
            header('Link: </js/app.js>; rel=preload; as=script', false);
        }
    }

    /**
     * Check if the request is for a static asset
     */
    private function isStaticAsset(Request $request): bool
    {
        $path = $request->getPathInfo();
        return preg_match('/\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$/', $path);
    }
}
