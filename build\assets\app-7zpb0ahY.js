import{e as qr,L as Hr}from"./notifications-I8teCTYo.js";var Yr=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function zr(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Qn={exports:{}};/*!
 * pace.js v1.2.4
 * https://github.com/CodeByZach/pace/
 * Licensed MIT © HubSpot, Inc.
 */(function(e,t){(function(){var n,r,s,o,i,a,l,u,c,E,g,I,f,m,P,S,b,C,M,w,F,W,A,B,Y,N,U,X,y,V,p,D,G,v,z,k,Se,oe,le,Ye,H,Z,fe,Te,re,be,jt,_t,Qt,Zt,ze=[].slice,Kt={}.hasOwnProperty,Jt=function(_,d){for(var T in d)Kt.call(d,T)&&(_[T]=d[T]);function R(){this.constructor=_}return R.prototype=d.prototype,_.prototype=new R,_.__super__=d.prototype,_},Vr=[].indexOf||function(_){for(var d=0,T=this.length;d<T;d++)if(d in this&&this[d]===_)return d;return-1},en=function(_,d){return function(){return _.apply(d,arguments)}};for(W={className:"",catchupTime:100,initialRate:.03,minTime:250,ghostTime:100,maxProgressPerFrame:20,easeFactor:1.25,startOnPageLoad:!0,restartOnPushState:!0,restartOnRequestAfter:500,target:"body",elements:{checkInterval:100,selectors:["body"]},eventLag:{minSamples:10,sampleCount:3,lagThreshold:3},ajax:{trackMethods:["GET"],trackWebSockets:!0,ignoreURLs:[]}},V=function(){var _;return(_=typeof performance<"u"&&performance!==null&&typeof performance.now=="function"?performance.now():void 0)!=null?_:+new Date},D=window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||window.msRequestAnimationFrame,F=window.cancelAnimationFrame||window.mozCancelAnimationFrame,S=function(_,d,T){return typeof _.addEventListener=="function"?_.addEventListener(d,T,!1):function(){if(typeof _["on"+d]!="function"||typeof _["on"+d].eventListeners!="object"){var R=new u;typeof _["on"+d]=="function"&&R.on(d,_["on"+d]),_["on"+d]=function(O){return R.trigger(d,O)},_["on"+d].eventListeners=R}else var R=_["on"+d].eventListeners;R.on(d,T)}()},D==null&&(D=function(_){return setTimeout(_,50)},F=function(_){return clearTimeout(_)}),v=function(_){var d,T;return d=V(),T=function(){var R;return R=V()-d,R>=33?(d=V(),_(R,function(){return D(T)})):setTimeout(T,33-R)},T()},G=function(){var _,d,T;return T=arguments[0],d=arguments[1],_=3<=arguments.length?ze.call(arguments,2):[],typeof T[d]=="function"?T[d].apply(T,_):T[d]},A=function(){var _,d,T,R,O,L,q;for(d=arguments[0],R=2<=arguments.length?ze.call(arguments,1):[],L=0,q=R.length;L<q;L++)if(T=R[L],T)for(_ in T)Kt.call(T,_)&&(O=T[_],d[_]!=null&&typeof d[_]=="object"&&O!=null&&typeof O=="object"?A(d[_],O):d[_]=O);return d},C=function(_){var d,T,R,O,L;for(T=d=0,O=0,L=_.length;O<L;O++)R=_[O],T+=Math.abs(R),d++;return T/d},Y=function(_,d){var T,R,O;if(_==null&&(_="options"),d==null&&(d=!0),O=document.querySelector("[data-pace-"+_+"]"),!!O){if(T=O.getAttribute("data-pace-"+_),!d)return T;try{return JSON.parse(T)}catch(L){return R=L,typeof console<"u"&&console!==null?console.error("Error parsing inline pace options",R):void 0}}},l=function(){function _(){}return _.prototype.on=function(d,T,R,O){var L;return O==null&&(O=!1),this.bindings==null&&(this.bindings={}),(L=this.bindings)[d]==null&&(L[d]=[]),this.bindings[d].push({handler:T,ctx:R,once:O})},_.prototype.once=function(d,T,R){return this.on(d,T,R,!0)},_.prototype.off=function(d,T){var R,O,L;if(((O=this.bindings)!=null?O[d]:void 0)!=null){if(T==null)return delete this.bindings[d];for(R=0,L=[];R<this.bindings[d].length;)this.bindings[d][R].handler===T?L.push(this.bindings[d].splice(R,1)):L.push(R++);return L}},_.prototype.trigger=function(){var d,T,R,O,L,q,$,j,K;if(R=arguments[0],d=2<=arguments.length?ze.call(arguments,1):[],($=this.bindings)!=null&&$[R]){for(L=0,K=[];L<this.bindings[R].length;)j=this.bindings[R][L],O=j.handler,T=j.ctx,q=j.once,O.apply(T??this,d),q?K.push(this.bindings[R].splice(L,1)):K.push(L++);return K}},_}(),E=window.Pace||{},window.Pace=E,A(E,l.prototype),p=E.options=A({},W,window.paceOptions,Y()),_t=["ajax","document","eventLag","elements"],Te=0,be=_t.length;Te<be;Te++)oe=_t[Te],p[oe]===!0&&(p[oe]=W[oe]);c=function(_){Jt(d,_);function d(){return Qt=d.__super__.constructor.apply(this,arguments),Qt}return d}(Error),r=function(){function _(){this.progress=0}return _.prototype.getElement=function(){var d;if(this.el==null){if(d=document.querySelector(p.target),!d)throw new c;this.el=document.createElement("div"),this.el.className="pace pace-active",document.body.className=document.body.className.replace(/(pace-done )|/,"pace-running ");var T=p.className!==""?" "+p.className:"";this.el.innerHTML='<div class="pace-progress'+T+`">
  <div class="pace-progress-inner"></div>
</div>
<div class="pace-activity"></div>`,d.firstChild!=null?d.insertBefore(this.el,d.firstChild):d.appendChild(this.el)}return this.el},_.prototype.finish=function(){var d;return d=this.getElement(),d.className=d.className.replace("pace-active","pace-inactive"),document.body.className=document.body.className.replace("pace-running ","pace-done ")},_.prototype.update=function(d){return this.progress=d,E.trigger("progress",d),this.render()},_.prototype.destroy=function(){try{this.getElement().parentNode.removeChild(this.getElement())}catch(d){c=d}return this.el=void 0},_.prototype.render=function(){var d,T,R,O,L,q,$;if(document.querySelector(p.target)==null)return!1;for(d=this.getElement(),O="translate3d("+this.progress+"%, 0, 0)",$=["webkitTransform","msTransform","transform"],L=0,q=$.length;L<q;L++)T=$[L],d.children[0].style[T]=O;return(!this.lastRenderedProgress||this.lastRenderedProgress|this.progress!==0|0)&&(d.children[0].setAttribute("data-progress-text",""+(this.progress|0)+"%"),this.progress>=100?R="99":(R=this.progress<10?"0":"",R+=this.progress|0),d.children[0].setAttribute("data-progress",""+R)),E.trigger("change",this.progress),this.lastRenderedProgress=this.progress},_.prototype.done=function(){return this.progress>=100},_}(),u=function(){function _(){this.bindings={}}return _.prototype.trigger=function(d,T){var R,O,L,q,$;if(this.bindings[d]!=null){for(q=this.bindings[d],$=[],O=0,L=q.length;O<L;O++)R=q[O],$.push(R.call(this,T));return $}},_.prototype.on=function(d,T){var R;return(R=this.bindings)[d]==null&&(R[d]=[]),this.bindings[d].push(T)},_}(),fe=window.XMLHttpRequest,Z=window.XDomainRequest,H=window.WebSocket,B=function(_,d){var T,R;R=[];for(T in d.prototype)try{_[T]==null&&typeof d[T]!="function"?typeof Object.defineProperty=="function"?R.push(Object.defineProperty(_,T,{get:function(O){return function(){return d.prototype[O]}}(T),configurable:!0,enumerable:!0})):R.push(_[T]=d.prototype[T]):R.push(void 0)}catch{}return R},X=[],E.ignore=function(){var _,d,T;return d=arguments[0],_=2<=arguments.length?ze.call(arguments,1):[],X.unshift("ignore"),T=d.apply(null,_),X.shift(),T},E.track=function(){var _,d,T;return d=arguments[0],_=2<=arguments.length?ze.call(arguments,1):[],X.unshift("track"),T=d.apply(null,_),X.shift(),T},Se=function(_){var d;if(_==null&&(_="GET"),X[0]==="track")return"force";if(!X.length&&p.ajax){if(_==="socket"&&p.ajax.trackWebSockets)return!0;if(d=_.toUpperCase(),Vr.call(p.ajax.trackMethods,d)>=0)return!0}return!1},g=function(_){Jt(d,_);function d(){var T,R=this;d.__super__.constructor.apply(this,arguments),T=function(O){var L;return L=O.open,O.open=function(q,$,j){return Se(q)&&R.trigger("request",{type:q,url:$,request:O}),L.apply(O,arguments)}},window.XMLHttpRequest=function(O){var L;return L=new fe(O),T(L),L};try{B(window.XMLHttpRequest,fe)}catch{}if(Z!=null){window.XDomainRequest=function(){var O;return O=new Z,T(O),O};try{B(window.XDomainRequest,Z)}catch{}}if(H!=null&&p.ajax.trackWebSockets){window.WebSocket=function(O,L){var q;return L!=null?q=new H(O,L):q=new H(O),Se("socket")&&R.trigger("request",{type:"socket",url:O,protocols:L,request:q}),q};try{B(window.WebSocket,H)}catch{}}}return d}(u),re=null,N=function(){return re==null&&(re=new g),re},k=function(_){var d,T,R,O;for(O=p.ajax.ignoreURLs,T=0,R=O.length;T<R;T++)if(d=O[T],typeof d=="string"){if(_.indexOf(d)!==-1)return!0}else if(d.test(_))return!0;return!1},N().on("request",function(_){var d,T,R,O,L;if(O=_.type,R=_.request,L=_.url,!k(L)&&!E.running&&(p.restartOnRequestAfter!==!1||Se(O)==="force"))return T=arguments,d=p.restartOnRequestAfter||0,typeof d=="boolean"&&(d=0),setTimeout(function(){var q,$,j,K,De,ye;if(O==="socket"?q=R.readyState<1:q=0<(K=R.readyState)&&K<4,q){for(E.restart(),De=E.sources,ye=[],$=0,j=De.length;$<j;$++)if(oe=De[$],oe instanceof n){oe.watch.apply(oe,T);break}else ye.push(void 0);return ye}},d)}),n=function(){function _(){this.complete=en(this.complete,this);var d=this;this.elements=[],N().on("request",function(){return d.watch.apply(d,arguments)})}return _.prototype.watch=function(d){var T,R,O,L;if(O=d.type,T=d.request,L=d.url,!k(L))return O==="socket"?R=new m(T,this.complete):R=new P(T,this.complete),this.elements.push(R)},_.prototype.complete=function(d){return this.elements=this.elements.filter(function(T){return T!==d})},_}(),P=function(){function _(d,T){var R,O,L,q,$,j=this;if(this.progress=0,window.ProgressEvent!=null)for(S(d,"progress",function(K){return K.lengthComputable?j.progress=100*K.loaded/K.total:j.progress=j.progress+(100-j.progress)/2},!1),$=["load","abort","timeout","error"],O=0,L=$.length;O<L;O++)R=$[O],S(d,R,function(){return T(j),j.progress=100},!1);else q=d.onreadystatechange,d.onreadystatechange=function(){var K;return(K=d.readyState)===0||K===4?(T(j),j.progress=100):d.readyState===3&&(j.progress=50),typeof q=="function"?q.apply(null,arguments):void 0}}return _}(),m=function(){function _(d,T){var R,O,L,q,$=this;for(this.progress=0,q=["error","open"],O=0,L=q.length;O<L;O++)R=q[O],S(d,R,function(){return T($),$.progress=100},!1)}return _}(),o=function(){function _(d){var T,R,O,L;for(d==null&&(d={}),this.complete=en(this.complete,this),this.elements=[],d.selectors==null&&(d.selectors=[]),L=d.selectors,R=0,O=L.length;R<O;R++)T=L[R],this.elements.push(new i(T,this.complete))}return _.prototype.complete=function(d){return this.elements=this.elements.filter(function(T){return T!==d})},_}(),i=function(){function _(d,T){this.selector=d,this.completeCallback=T,this.progress=0,this.check()}return _.prototype.check=function(){var d=this;return document.querySelector(this.selector)?this.done():setTimeout(function(){return d.check()},p.elements.checkInterval)},_.prototype.done=function(){return this.completeCallback(this),this.completeCallback=null,this.progress=100},_}(),s=function(){_.prototype.states={loading:0,interactive:50,complete:100};function _(){var d,T,R=this;this.progress=(T=this.states[document.readyState])!=null?T:100,d=document.onreadystatechange,document.onreadystatechange=function(){return R.states[document.readyState]!=null&&(R.progress=R.states[document.readyState]),typeof d=="function"?d.apply(null,arguments):void 0}}return _}(),a=function(){function _(){var d,T,R,O,L,q=this;this.progress=0,d=0,L=[],O=0,R=V(),T=setInterval(function(){var $;return $=V()-R-50,R=V(),L.push($),L.length>p.eventLag.sampleCount&&L.shift(),d=C(L),++O>=p.eventLag.minSamples&&d<p.eventLag.lagThreshold?(q.progress=100,clearInterval(T)):q.progress=100*(3/(d+3))},50)}return _}(),f=function(){function _(d){this.source=d,this.last=this.sinceLastUpdate=0,this.rate=p.initialRate,this.catchup=0,this.progress=this.lastProgress=0,this.source!=null&&(this.progress=G(this.source,"progress"))}return _.prototype.tick=function(d,T){var R;return T==null&&(T=G(this.source,"progress")),T>=100&&(this.done=!0),T===this.last?this.sinceLastUpdate+=d:(this.sinceLastUpdate&&(this.rate=(T-this.last)/this.sinceLastUpdate),this.catchup=(T-this.progress)/p.catchupTime,this.sinceLastUpdate=0,this.last=T),T>this.progress&&(this.progress+=this.catchup*d),R=1-Math.pow(this.progress/100,p.easeFactor),this.progress+=R*this.rate*d,this.progress=Math.min(this.lastProgress+p.maxProgressPerFrame,this.progress),this.progress=Math.max(0,this.progress),this.progress=Math.min(100,this.progress),this.lastProgress=this.progress,this.progress},_}(),le=null,z=null,M=null,Ye=null,b=null,w=null,E.running=!1,U=function(){if(p.restartOnPushState)return E.restart()},window.history.pushState!=null&&(jt=window.history.pushState,window.history.pushState=function(){return U(),jt.apply(window.history,arguments)}),window.history.replaceState!=null&&(Zt=window.history.replaceState,window.history.replaceState=function(){return U(),Zt.apply(window.history,arguments)}),I={ajax:n,elements:o,document:s,eventLag:a},(y=function(){var _,d,T,R,O,L,q,$;for(E.sources=le=[],L=["ajax","elements","document","eventLag"],d=0,R=L.length;d<R;d++)_=L[d],p[_]!==!1&&le.push(new I[_](p[_]));for($=(q=p.extraSources)!=null?q:[],T=0,O=$.length;T<O;T++)oe=$[T],le.push(new oe(p));return E.bar=M=new r,z=[],Ye=new f})(),E.stop=function(){return E.trigger("stop"),E.running=!1,M.destroy(),w=!0,b!=null&&(typeof F=="function"&&F(b),b=null),y()},E.restart=function(){return E.trigger("restart"),E.stop(),E.start()},E.go=function(){var _;return E.running=!0,M.render(),_=V(),w=!1,b=v(function(d,T){var R,O,L,q,$,j,K,De,ye,It,Tt,mt,tn,nn,rn;for(100-M.progress,O=It=0,L=!0,j=Tt=0,tn=le.length;Tt<tn;j=++Tt)for(oe=le[j],ye=z[j]!=null?z[j]:z[j]=[],$=(rn=oe.elements)!=null?rn:[oe],K=mt=0,nn=$.length;mt<nn;K=++mt)q=$[K],De=ye[K]!=null?ye[K]:ye[K]=new f(q),L&=De.done,!De.done&&(O++,It+=De.tick(d));return R=It/O,M.update(Ye.tick(d,R)),M.done()||L||w?(M.update(100),E.trigger("done"),setTimeout(function(){return M.finish(),E.running=!1,E.trigger("hide")},Math.max(p.ghostTime,Math.max(p.minTime-(V()-_),0)))):T()})},E.start=function(_){A(p,_),E.running=!0;try{M.render()}catch(d){c=d}return document.querySelector(".pace")?(E.trigger("start"),E.go()):setTimeout(E.start,50)},e.exports=E}).call(Yr)})(Qn);var $r=Qn.exports;const Wr=zr($r);Wr.start();/*!
 * FilePond 4.30.6
 * Licensed under MIT, https://opensource.org/licenses/MIT/
 * Please visit https://pqina.nl/filepond/ for details.
 */const kr=e=>e instanceof HTMLElement,Xr=(e,t=[],n=[])=>{const r={...e},s=[],o=[],i=()=>({...r}),a=()=>{const f=[...s];return s.length=0,f},l=()=>{const f=[...o];o.length=0,f.forEach(({type:m,data:P})=>{u(m,P)})},u=(f,m,P)=>{if(P&&!document.hidden){o.push({type:f,data:m});return}I[f]&&I[f](m),s.push({type:f,data:m})},c=(f,...m)=>g[f]?g[f](...m):null,E={getState:i,processActionQueue:a,processDispatchQueue:l,dispatch:u,query:c};let g={};t.forEach(f=>{g={...f(r),...g}});let I={};return n.forEach(f=>{I={...f(u,c,r),...I}}),E},jr=(e,t,n)=>{if(typeof n=="function"){e[t]=n;return}Object.defineProperty(e,t,{...n})},Q=(e,t)=>{for(const n in e)e.hasOwnProperty(n)&&t(n,e[n])},Le=e=>{const t={};return Q(e,n=>{jr(t,n,e[n])}),t},te=(e,t,n=null)=>{if(n===null)return e.getAttribute(t)||e.hasAttribute(t);e.setAttribute(t,n)},Qr="http://www.w3.org/2000/svg",Zr=["svg","path"],sn=e=>Zr.includes(e),st=(e,t,n={})=>{typeof t=="object"&&(n=t,t=null);const r=sn(e)?document.createElementNS(Qr,e):document.createElement(e);return t&&(sn(e)?te(r,"class",t):r.className=t),Q(n,(s,o)=>{te(r,s,o)}),r},Kr=e=>(t,n)=>{typeof n<"u"&&e.children[n]?e.insertBefore(t,e.children[n]):e.appendChild(t)},Jr=(e,t)=>(n,r)=>(typeof r<"u"?t.splice(r,0,n):t.push(n),n),es=(e,t)=>n=>(t.splice(t.indexOf(n),1),n.element.parentNode&&e.removeChild(n.element),n),ts=typeof window<"u"&&typeof window.document<"u",Zn=()=>ts,ns=Zn()?st("svg"):{},rs="children"in ns?e=>e.children.length:e=>e.childNodes.length,Kn=(e,t,n,r)=>{const s=n[0]||e.left,o=n[1]||e.top,i=s+e.width,a=o+e.height*(r[1]||1),l={element:{...e},inner:{left:e.left,top:e.top,right:e.right,bottom:e.bottom},outer:{left:s,top:o,right:i,bottom:a}};return t.filter(u=>!u.isRectIgnored()).map(u=>u.rect).forEach(u=>{on(l.inner,{...u.inner}),on(l.outer,{...u.outer})}),ln(l.inner),l.outer.bottom+=l.element.marginBottom,l.outer.right+=l.element.marginRight,ln(l.outer),l},on=(e,t)=>{t.top+=e.top,t.right+=e.left,t.bottom+=e.top,t.left+=e.left,t.bottom>e.bottom&&(e.bottom=t.bottom),t.right>e.right&&(e.right=t.right)},ln=e=>{e.width=e.right-e.left,e.height=e.bottom-e.top},Pe=e=>typeof e=="number",ss=(e,t,n,r=.001)=>Math.abs(e-t)<r&&Math.abs(n)<r,is=({stiffness:e=.5,damping:t=.75,mass:n=10}={})=>{let r=null,s=null,o=0,i=!1;const u=Le({interpolate:(c,E)=>{if(i)return;if(!(Pe(r)&&Pe(s))){i=!0,o=0;return}const g=-(s-r)*e;o+=g/n,s+=o,o*=t,ss(s,r,o)||E?(s=r,o=0,i=!0,u.onupdate(s),u.oncomplete(s)):u.onupdate(s)},target:{set:c=>{if(Pe(c)&&!Pe(s)&&(s=c),r===null&&(r=c,s=c),r=c,s===r||typeof r>"u"){i=!0,o=0,u.onupdate(s),u.oncomplete(s);return}i=!1},get:()=>r},resting:{get:()=>i},onupdate:c=>{},oncomplete:c=>{}});return u},os=e=>e<.5?2*e*e:-1+(4-2*e)*e,ls=({duration:e=500,easing:t=os,delay:n=0}={})=>{let r=null,s,o,i=!0,a=!1,l=null;const c=Le({interpolate:(E,g)=>{i||l===null||(r===null&&(r=E),!(E-r<n)&&(s=E-r-n,s>=e||g?(s=1,o=a?0:1,c.onupdate(o*l),c.oncomplete(o*l),i=!0):(o=s/e,c.onupdate((s>=0?t(a?1-o:o):0)*l))))},target:{get:()=>a?0:l,set:E=>{if(l===null){l=E,c.onupdate(E),c.oncomplete(E);return}E<l?(l=1,a=!0):(a=!1,l=E),i=!1,r=null}},resting:{get:()=>i},onupdate:E=>{},oncomplete:E=>{}});return c},an={spring:is,tween:ls},as=(e,t,n)=>{const r=e[t]&&typeof e[t][n]=="object"?e[t][n]:e[t]||e,s=typeof r=="string"?r:r.type,o=typeof r=="object"?{...r}:{};return an[s]?an[s](o):null},Ft=(e,t,n,r=!1)=>{t=Array.isArray(t)?t:[t],t.forEach(s=>{e.forEach(o=>{let i=o,a=()=>n[o],l=u=>n[o]=u;typeof o=="object"&&(i=o.key,a=o.getter||a,l=o.setter||l),!(s[i]&&!r)&&(s[i]={get:a,set:l})})})},cs=({mixinConfig:e,viewProps:t,viewInternalAPI:n,viewExternalAPI:r})=>{const s={...t},o=[];return Q(e,(i,a)=>{const l=as(a);if(!l)return;l.onupdate=c=>{t[i]=c},l.target=s[i],Ft([{key:i,setter:c=>{l.target!==c&&(l.target=c)},getter:()=>t[i]}],[n,r],t,!0),o.push(l)}),{write:i=>{let a=document.hidden,l=!0;return o.forEach(u=>{u.resting||(l=!1),u.interpolate(i,a)}),l},destroy:()=>{}}},us=e=>(t,n)=>{e.addEventListener(t,n)},ds=e=>(t,n)=>{e.removeEventListener(t,n)},fs=({mixinConfig:e,viewProps:t,viewInternalAPI:n,viewExternalAPI:r,viewState:s,view:o})=>{const i=[],a=us(o.element),l=ds(o.element);return r.on=(u,c)=>{i.push({type:u,fn:c}),a(u,c)},r.off=(u,c)=>{i.splice(i.findIndex(E=>E.type===u&&E.fn===c),1),l(u,c)},{write:()=>!0,destroy:()=>{i.forEach(u=>{l(u.type,u.fn)})}}},Es=({mixinConfig:e,viewProps:t,viewExternalAPI:n})=>{Ft(e,n,t)},ae=e=>e!=null,ps={opacity:1,scaleX:1,scaleY:1,translateX:0,translateY:0,rotateX:0,rotateY:0,rotateZ:0,originX:0,originY:0},_s=({mixinConfig:e,viewProps:t,viewInternalAPI:n,viewExternalAPI:r,view:s})=>{const o={...t},i={};Ft(e,[n,r],t);const a=()=>[t.translateX||0,t.translateY||0],l=()=>[t.scaleX||0,t.scaleY||0],u=()=>s.rect?Kn(s.rect,s.childViews,a(),l()):null;return n.rect={get:u},r.rect={get:u},e.forEach(c=>{t[c]=typeof o[c]>"u"?ps[c]:o[c]}),{write:()=>{if(Is(i,t))return Ts(s.element,t),Object.assign(i,{...t}),!0},destroy:()=>{}}},Is=(e,t)=>{if(Object.keys(e).length!==Object.keys(t).length)return!0;for(const n in t)if(t[n]!==e[n])return!0;return!1},Ts=(e,{opacity:t,perspective:n,translateX:r,translateY:s,scaleX:o,scaleY:i,rotateX:a,rotateY:l,rotateZ:u,originX:c,originY:E,width:g,height:I})=>{let f="",m="";(ae(c)||ae(E))&&(m+=`transform-origin: ${c||0}px ${E||0}px;`),ae(n)&&(f+=`perspective(${n}px) `),(ae(r)||ae(s))&&(f+=`translate3d(${r||0}px, ${s||0}px, 0) `),(ae(o)||ae(i))&&(f+=`scale3d(${ae(o)?o:1}, ${ae(i)?i:1}, 1) `),ae(u)&&(f+=`rotateZ(${u}rad) `),ae(a)&&(f+=`rotateX(${a}rad) `),ae(l)&&(f+=`rotateY(${l}rad) `),f.length&&(m+=`transform:${f};`),ae(t)&&(m+=`opacity:${t};`,t===0&&(m+="visibility:hidden;"),t<1&&(m+="pointer-events:none;")),ae(I)&&(m+=`height:${I}px;`),ae(g)&&(m+=`width:${g}px;`);const P=e.elementCurrentStyle||"";(m.length!==P.length||m!==P)&&(e.style.cssText=m,e.elementCurrentStyle=m)},ms={styles:_s,listeners:fs,animations:cs,apis:Es},cn=(e={},t={},n={})=>(t.layoutCalculated||(e.paddingTop=parseInt(n.paddingTop,10)||0,e.marginTop=parseInt(n.marginTop,10)||0,e.marginRight=parseInt(n.marginRight,10)||0,e.marginBottom=parseInt(n.marginBottom,10)||0,e.marginLeft=parseInt(n.marginLeft,10)||0,t.layoutCalculated=!0),e.left=t.offsetLeft||0,e.top=t.offsetTop||0,e.width=t.offsetWidth||0,e.height=t.offsetHeight||0,e.right=e.left+e.width,e.bottom=e.top+e.height,e.scrollTop=t.scrollTop,e.hidden=t.offsetParent===null,e),ne=({tag:e="div",name:t=null,attributes:n={},read:r=()=>{},write:s=()=>{},create:o=()=>{},destroy:i=()=>{},filterFrameActionsForChild:a=(I,f)=>f,didCreateView:l=()=>{},didWriteView:u=()=>{},ignoreRect:c=!1,ignoreRectUpdate:E=!1,mixins:g=[]}={})=>(I,f={})=>{const m=st(e,`filepond--${t}`,n),P=window.getComputedStyle(m,null),S=cn();let b=null,C=!1;const M=[],w=[],F={},W={},A=[s],B=[r],Y=[i],N=()=>m,U=()=>M.concat(),X=()=>F,y=H=>(Z,fe)=>Z(H,fe),V=()=>b||(b=Kn(S,M,[0,0],[1,1]),b),p=()=>P,D=()=>{b=null,M.forEach(fe=>fe._read()),!(E&&S.width&&S.height)&&cn(S,m,P);const Z={root:le,props:f,rect:S};B.forEach(fe=>fe(Z))},G=(H,Z,fe)=>{let Te=Z.length===0;return A.forEach(re=>{re({props:f,root:le,actions:Z,timestamp:H,shouldOptimize:fe})===!1&&(Te=!1)}),w.forEach(re=>{re.write(H)===!1&&(Te=!1)}),M.filter(re=>!!re.element.parentNode).forEach(re=>{re._write(H,a(re,Z),fe)||(Te=!1)}),M.forEach((re,be)=>{re.element.parentNode||(le.appendChild(re.element,be),re._read(),re._write(H,a(re,Z),fe),Te=!1)}),C=Te,u({props:f,root:le,actions:Z,timestamp:H}),Te},v=()=>{w.forEach(H=>H.destroy()),Y.forEach(H=>{H({root:le,props:f})}),M.forEach(H=>H._destroy())},z={element:{get:N},style:{get:p},childViews:{get:U}},k={...z,rect:{get:V},ref:{get:X},is:H=>t===H,appendChild:Kr(m),createChildView:y(I),linkView:H=>(M.push(H),H),unlinkView:H=>{M.splice(M.indexOf(H),1)},appendChildView:Jr(m,M),removeChildView:es(m,M),registerWriter:H=>A.push(H),registerReader:H=>B.push(H),registerDestroyer:H=>Y.push(H),invalidateLayout:()=>m.layoutCalculated=!1,dispatch:I.dispatch,query:I.query},Se={element:{get:N},childViews:{get:U},rect:{get:V},resting:{get:()=>C},isRectIgnored:()=>c,_read:D,_write:G,_destroy:v},oe={...z,rect:{get:()=>S}};Object.keys(g).sort((H,Z)=>H==="styles"?1:Z==="styles"?-1:0).forEach(H=>{const Z=ms[H]({mixinConfig:g[H],viewProps:f,viewState:W,viewInternalAPI:k,viewExternalAPI:Se,view:Le(oe)});Z&&w.push(Z)});const le=Le(k);o({root:le,props:f});const Ye=rs(m);return M.forEach((H,Z)=>{le.appendChild(H.element,Ye+Z)}),l(le),Le(Se)},gs=(e,t,n=60)=>{const r="__framePainter";if(window[r]){window[r].readers.push(e),window[r].writers.push(t);return}window[r]={readers:[e],writers:[t]};const s=window[r],o=1e3/n;let i=null,a=null,l=null,u=null;const c=()=>{document.hidden?(l=()=>window.setTimeout(()=>E(performance.now()),o),u=()=>window.clearTimeout(a)):(l=()=>window.requestAnimationFrame(E),u=()=>window.cancelAnimationFrame(a))};document.addEventListener("visibilitychange",()=>{u&&u(),c(),E(performance.now())});const E=g=>{a=l(E),i||(i=g);const I=g-i;I<=o||(i=g-I%o,s.readers.forEach(f=>f()),s.writers.forEach(f=>f(g)))};return c(),E(performance.now()),{pause:()=>{u(a)}}},ue=(e,t)=>({root:n,props:r,actions:s=[],timestamp:o,shouldOptimize:i})=>{s.filter(a=>e[a.type]).forEach(a=>e[a.type]({root:n,props:r,action:a.data,timestamp:o,shouldOptimize:i})),t&&t({root:n,props:r,actions:s,timestamp:o,shouldOptimize:i})},un=(e,t)=>t.parentNode.insertBefore(e,t),dn=(e,t)=>t.parentNode.insertBefore(e,t.nextSibling),at=e=>Array.isArray(e),Re=e=>e==null,hs=e=>e.trim(),ct=e=>""+e,Rs=(e,t=",")=>Re(e)?[]:at(e)?e:ct(e).split(t).map(hs).filter(n=>n.length),Jn=e=>typeof e=="boolean",er=e=>Jn(e)?e:e==="true",ce=e=>typeof e=="string",tr=e=>Pe(e)?e:ce(e)?ct(e).replace(/[a-z]+/gi,""):0,nt=e=>parseInt(tr(e),10),fn=e=>parseFloat(tr(e)),He=e=>Pe(e)&&isFinite(e)&&Math.floor(e)===e,En=(e,t=1e3)=>{if(He(e))return e;let n=ct(e).trim();return/MB$/i.test(n)?(n=n.replace(/MB$i/,"").trim(),nt(n)*t*t):/KB/i.test(n)?(n=n.replace(/KB$i/,"").trim(),nt(n)*t):nt(n)},Me=e=>typeof e=="function",Os=e=>{let t=self,n=e.split("."),r=null;for(;r=n.shift();)if(t=t[r],!t)return null;return t},pn={process:"POST",patch:"PATCH",revert:"DELETE",fetch:"GET",restore:"GET",load:"GET"},Ss=e=>{const t={};return t.url=ce(e)?e:e.url||"",t.timeout=e.timeout?parseInt(e.timeout,10):0,t.headers=e.headers?e.headers:{},Q(pn,n=>{t[n]=Ds(n,e[n],pn[n],t.timeout,t.headers)}),t.process=e.process||ce(e)||e.url?t.process:null,t.remove=e.remove||null,delete t.headers,t},Ds=(e,t,n,r,s)=>{if(t===null)return null;if(typeof t=="function")return t;const o={url:n==="GET"||n==="PATCH"?`?${e}=`:"",method:n,headers:s,withCredentials:!1,timeout:r,onload:null,ondata:null,onerror:null};if(ce(t))return o.url=t,o;if(Object.assign(o,t),ce(o.headers)){const i=o.headers.split(/:(.+)/);o.headers={header:i[0],value:i[1]}}return o.withCredentials=er(o.withCredentials),o},ys=e=>Ss(e),As=e=>e===null,ie=e=>typeof e=="object"&&e!==null,Ls=e=>ie(e)&&ce(e.url)&&ie(e.process)&&ie(e.revert)&&ie(e.restore)&&ie(e.fetch),At=e=>at(e)?"array":As(e)?"null":He(e)?"int":/^[0-9]+ ?(?:GB|MB|KB)$/gi.test(e)?"bytes":Ls(e)?"api":typeof e,bs=e=>e.replace(/{\s*'/g,'{"').replace(/'\s*}/g,'"}').replace(/'\s*:/g,'":').replace(/:\s*'/g,':"').replace(/,\s*'/g,',"').replace(/'\s*,/g,'",'),Ps={array:Rs,boolean:er,int:e=>At(e)==="bytes"?En(e):nt(e),number:fn,float:fn,bytes:En,string:e=>Me(e)?e:ct(e),function:e=>Os(e),serverapi:ys,object:e=>{try{return JSON.parse(bs(e))}catch{return null}}},Ms=(e,t)=>Ps[t](e),nr=(e,t,n)=>{if(e===t)return e;let r=At(e);if(r!==n){const s=Ms(e,n);if(r=At(s),s===null)throw`Trying to assign value with incorrect type to "${option}", allowed type: "${n}"`;e=s}return e},ws=(e,t)=>{let n=e;return{enumerable:!0,get:()=>n,set:r=>{n=nr(r,e,t)}}},Cs=e=>{const t={};return Q(e,n=>{const r=e[n];t[n]=ws(r[0],r[1])}),Le(t)},vs=e=>({items:[],listUpdateTimeout:null,itemUpdateTimeout:null,processingQueue:[],options:Cs(e)}),ut=(e,t="-")=>e.split(/(?=[A-Z])/).map(n=>n.toLowerCase()).join(t),Ns=(e,t)=>{const n={};return Q(t,r=>{n[r]={get:()=>e.getState().options[r],set:s=>{e.dispatch(`SET_${ut(r,"_").toUpperCase()}`,{value:s})}}}),n},Gs=e=>(t,n,r)=>{const s={};return Q(e,o=>{const i=ut(o,"_").toUpperCase();s[`SET_${i}`]=a=>{try{r.options[o]=a.value}catch{}t(`DID_SET_${i}`,{value:r.options[o]})}}),s},Fs=e=>t=>{const n={};return Q(e,r=>{n[`GET_${ut(r,"_").toUpperCase()}`]=s=>t.options[r]}),n},Ie={API:1,DROP:2,BROWSE:3,PASTE:4,NONE:5},Bt=()=>Math.random().toString(36).substring(2,11),Ut=(e,t)=>e.splice(t,1),Bs=(e,t)=>{t?e():document.hidden?Promise.resolve(1).then(e):setTimeout(e,0)},dt=()=>{const e=[],t=(r,s)=>{Ut(e,e.findIndex(o=>o.event===r&&(o.cb===s||!s)))},n=(r,s,o)=>{e.filter(i=>i.event===r).map(i=>i.cb).forEach(i=>Bs(()=>i(...s),o))};return{fireSync:(r,...s)=>{n(r,s,!0)},fire:(r,...s)=>{n(r,s,!1)},on:(r,s)=>{e.push({event:r,cb:s})},onOnce:(r,s)=>{e.push({event:r,cb:(...o)=>{t(r,s),s(...o)}})},off:t}},rr=(e,t,n)=>{Object.getOwnPropertyNames(e).filter(r=>!n.includes(r)).forEach(r=>Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(e,r)))},Us=["fire","process","revert","load","on","off","onOnce","retryLoad","extend","archive","archived","release","released","requestProcessing","freeze"],de=e=>{const t={};return rr(e,t,Us),t},xs=e=>{e.forEach((t,n)=>{t.released&&Ut(e,n)})},x={INIT:1,IDLE:2,PROCESSING_QUEUED:9,PROCESSING:3,PROCESSING_COMPLETE:5,PROCESSING_ERROR:6,PROCESSING_REVERT_ERROR:10,LOADING:7,LOAD_ERROR:8},se={INPUT:1,LIMBO:2,LOCAL:3},sr=e=>/[^0-9]+/.exec(e),ir=()=>sr(1.1.toLocaleString())[0],Vs=()=>{const e=ir(),t=1e3.toLocaleString();return t!=="1000"?sr(t)[0]:e==="."?",":"."},h={BOOLEAN:"boolean",INT:"int",NUMBER:"number",STRING:"string",ARRAY:"array",OBJECT:"object",FUNCTION:"function",ACTION:"action",SERVER_API:"serverapi",REGEX:"regex"},xt=[],me=(e,t,n)=>new Promise((r,s)=>{const o=xt.filter(a=>a.key===e).map(a=>a.cb);if(o.length===0){r(t);return}const i=o.shift();o.reduce((a,l)=>a.then(u=>l(u,n)),i(t,n)).then(a=>r(a)).catch(a=>s(a))}),Ge=(e,t,n)=>xt.filter(r=>r.key===e).map(r=>r.cb(t,n)),qs=(e,t)=>xt.push({key:e,cb:t}),Hs=e=>Object.assign(Be,e),it=()=>({...Be}),Ys=e=>{Q(e,(t,n)=>{Be[t]&&(Be[t][0]=nr(n,Be[t][0],Be[t][1]))})},Be={id:[null,h.STRING],name:["filepond",h.STRING],disabled:[!1,h.BOOLEAN],className:[null,h.STRING],required:[!1,h.BOOLEAN],captureMethod:[null,h.STRING],allowSyncAcceptAttribute:[!0,h.BOOLEAN],allowDrop:[!0,h.BOOLEAN],allowBrowse:[!0,h.BOOLEAN],allowPaste:[!0,h.BOOLEAN],allowMultiple:[!1,h.BOOLEAN],allowReplace:[!0,h.BOOLEAN],allowRevert:[!0,h.BOOLEAN],allowRemove:[!0,h.BOOLEAN],allowProcess:[!0,h.BOOLEAN],allowReorder:[!1,h.BOOLEAN],allowDirectoriesOnly:[!1,h.BOOLEAN],storeAsFile:[!1,h.BOOLEAN],forceRevert:[!1,h.BOOLEAN],maxFiles:[null,h.INT],checkValidity:[!1,h.BOOLEAN],itemInsertLocationFreedom:[!0,h.BOOLEAN],itemInsertLocation:["before",h.STRING],itemInsertInterval:[75,h.INT],dropOnPage:[!1,h.BOOLEAN],dropOnElement:[!0,h.BOOLEAN],dropValidation:[!1,h.BOOLEAN],ignoredFiles:[[".ds_store","thumbs.db","desktop.ini"],h.ARRAY],instantUpload:[!0,h.BOOLEAN],maxParallelUploads:[2,h.INT],allowMinimumUploadDuration:[!0,h.BOOLEAN],chunkUploads:[!1,h.BOOLEAN],chunkForce:[!1,h.BOOLEAN],chunkSize:[5e6,h.INT],chunkRetryDelays:[[500,1e3,3e3],h.ARRAY],server:[null,h.SERVER_API],fileSizeBase:[1e3,h.INT],labelFileSizeBytes:["bytes",h.STRING],labelFileSizeKilobytes:["KB",h.STRING],labelFileSizeMegabytes:["MB",h.STRING],labelFileSizeGigabytes:["GB",h.STRING],labelDecimalSeparator:[ir(),h.STRING],labelThousandsSeparator:[Vs(),h.STRING],labelIdle:['Drag & Drop your files or <span class="filepond--label-action">Browse</span>',h.STRING],labelInvalidField:["Field contains invalid files",h.STRING],labelFileWaitingForSize:["Waiting for size",h.STRING],labelFileSizeNotAvailable:["Size not available",h.STRING],labelFileCountSingular:["file in list",h.STRING],labelFileCountPlural:["files in list",h.STRING],labelFileLoading:["Loading",h.STRING],labelFileAdded:["Added",h.STRING],labelFileLoadError:["Error during load",h.STRING],labelFileRemoved:["Removed",h.STRING],labelFileRemoveError:["Error during remove",h.STRING],labelFileProcessing:["Uploading",h.STRING],labelFileProcessingComplete:["Upload complete",h.STRING],labelFileProcessingAborted:["Upload cancelled",h.STRING],labelFileProcessingError:["Error during upload",h.STRING],labelFileProcessingRevertError:["Error during revert",h.STRING],labelTapToCancel:["tap to cancel",h.STRING],labelTapToRetry:["tap to retry",h.STRING],labelTapToUndo:["tap to undo",h.STRING],labelButtonRemoveItem:["Remove",h.STRING],labelButtonAbortItemLoad:["Abort",h.STRING],labelButtonRetryItemLoad:["Retry",h.STRING],labelButtonAbortItemProcessing:["Cancel",h.STRING],labelButtonUndoItemProcessing:["Undo",h.STRING],labelButtonRetryItemProcessing:["Retry",h.STRING],labelButtonProcessItem:["Upload",h.STRING],iconRemove:['<svg width="26" height="26" viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg"><path d="M11.586 13l-2.293 2.293a1 1 0 0 0 1.414 1.414L13 14.414l2.293 2.293a1 1 0 0 0 1.414-1.414L14.414 13l2.293-2.293a1 1 0 0 0-1.414-1.414L13 11.586l-2.293-2.293a1 1 0 0 0-1.414 1.414L11.586 13z" fill="currentColor" fill-rule="nonzero"/></svg>',h.STRING],iconProcess:['<svg width="26" height="26" viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg"><path d="M14 10.414v3.585a1 1 0 0 1-2 0v-3.585l-1.293 1.293a1 1 0 0 1-1.414-1.415l3-3a1 1 0 0 1 1.414 0l3 3a1 1 0 0 1-1.414 1.415L14 10.414zM9 18a1 1 0 0 1 0-2h8a1 1 0 0 1 0 2H9z" fill="currentColor" fill-rule="evenodd"/></svg>',h.STRING],iconRetry:['<svg width="26" height="26" viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg"><path d="M10.81 9.185l-.038.02A4.997 4.997 0 0 0 8 13.683a5 5 0 0 0 5 5 5 5 0 0 0 5-5 1 1 0 0 1 2 0A7 7 0 1 1 9.722 7.496l-.842-.21a.999.999 0 1 1 .484-1.94l3.23.806c.535.133.86.675.73 1.21l-.804 3.233a.997.997 0 0 1-1.21.73.997.997 0 0 1-.73-1.21l.23-.928v-.002z" fill="currentColor" fill-rule="nonzero"/></svg>',h.STRING],iconUndo:['<svg width="26" height="26" viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg"><path d="M9.185 10.81l.02-.038A4.997 4.997 0 0 1 13.683 8a5 5 0 0 1 5 5 5 5 0 0 1-5 5 1 1 0 0 0 0 2A7 7 0 1 0 7.496 9.722l-.21-.842a.999.999 0 1 0-1.94.484l.806 3.23c.133.535.675.86 1.21.73l3.233-.803a.997.997 0 0 0 .73-1.21.997.997 0 0 0-1.21-.73l-.928.23-.002-.001z" fill="currentColor" fill-rule="nonzero"/></svg>',h.STRING],iconDone:['<svg width="26" height="26" viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg"><path d="M18.293 9.293a1 1 0 0 1 1.414 1.414l-7.002 7a1 1 0 0 1-1.414 0l-3.998-4a1 1 0 1 1 1.414-1.414L12 15.586l6.294-6.293z" fill="currentColor" fill-rule="nonzero"/></svg>',h.STRING],oninit:[null,h.FUNCTION],onwarning:[null,h.FUNCTION],onerror:[null,h.FUNCTION],onactivatefile:[null,h.FUNCTION],oninitfile:[null,h.FUNCTION],onaddfilestart:[null,h.FUNCTION],onaddfileprogress:[null,h.FUNCTION],onaddfile:[null,h.FUNCTION],onprocessfilestart:[null,h.FUNCTION],onprocessfileprogress:[null,h.FUNCTION],onprocessfileabort:[null,h.FUNCTION],onprocessfilerevert:[null,h.FUNCTION],onprocessfile:[null,h.FUNCTION],onprocessfiles:[null,h.FUNCTION],onremovefile:[null,h.FUNCTION],onpreparefile:[null,h.FUNCTION],onupdatefiles:[null,h.FUNCTION],onreorderfiles:[null,h.FUNCTION],beforeDropFile:[null,h.FUNCTION],beforeAddFile:[null,h.FUNCTION],beforeRemoveFile:[null,h.FUNCTION],beforePrepareFile:[null,h.FUNCTION],stylePanelLayout:[null,h.STRING],stylePanelAspectRatio:[null,h.STRING],styleItemPanelAspectRatio:[null,h.STRING],styleButtonRemoveItemPosition:["left",h.STRING],styleButtonProcessItemPosition:["right",h.STRING],styleLoadIndicatorPosition:["right",h.STRING],styleProgressIndicatorPosition:["right",h.STRING],styleButtonRemoveItemAlign:[!1,h.BOOLEAN],files:[[],h.ARRAY],credits:[["https://pqina.nl/","Powered by PQINA"],h.ARRAY]},we=(e,t)=>Re(t)?e[0]||null:He(t)?e[t]||null:(typeof t=="object"&&(t=t.id),e.find(n=>n.id===t)||null),or=e=>{if(Re(e))return e;if(/:/.test(e)){const t=e.split(":");return t[1]/t[0]}return parseFloat(e)},ge=e=>e.filter(t=>!t.archived),lr={EMPTY:0,IDLE:1,ERROR:2,BUSY:3,READY:4};let Qe=null;const zs=()=>{if(Qe===null)try{const e=new DataTransfer;e.items.add(new File(["hello world"],"This_Works.txt"));const t=document.createElement("input");t.setAttribute("type","file"),t.files=e.files,Qe=t.files.length===1}catch{Qe=!1}return Qe},$s=[x.LOAD_ERROR,x.PROCESSING_ERROR,x.PROCESSING_REVERT_ERROR],Ws=[x.LOADING,x.PROCESSING,x.PROCESSING_QUEUED,x.INIT],ks=[x.PROCESSING_COMPLETE],Xs=e=>$s.includes(e.status),js=e=>Ws.includes(e.status),Qs=e=>ks.includes(e.status),_n=e=>ie(e.options.server)&&(ie(e.options.server.process)||Me(e.options.server.process)),Zs=e=>({GET_STATUS:()=>{const t=ge(e.items),{EMPTY:n,ERROR:r,BUSY:s,IDLE:o,READY:i}=lr;return t.length===0?n:t.some(Xs)?r:t.some(js)?s:t.some(Qs)?i:o},GET_ITEM:t=>we(e.items,t),GET_ACTIVE_ITEM:t=>we(ge(e.items),t),GET_ACTIVE_ITEMS:()=>ge(e.items),GET_ITEMS:()=>e.items,GET_ITEM_NAME:t=>{const n=we(e.items,t);return n?n.filename:null},GET_ITEM_SIZE:t=>{const n=we(e.items,t);return n?n.fileSize:null},GET_STYLES:()=>Object.keys(e.options).filter(t=>/^style/.test(t)).map(t=>({name:t,value:e.options[t]})),GET_PANEL_ASPECT_RATIO:()=>/circle/.test(e.options.stylePanelLayout)?1:or(e.options.stylePanelAspectRatio),GET_ITEM_PANEL_ASPECT_RATIO:()=>e.options.styleItemPanelAspectRatio,GET_ITEMS_BY_STATUS:t=>ge(e.items).filter(n=>n.status===t),GET_TOTAL_ITEMS:()=>ge(e.items).length,SHOULD_UPDATE_FILE_INPUT:()=>e.options.storeAsFile&&zs()&&!_n(e),IS_ASYNC:()=>_n(e),GET_FILE_SIZE_LABELS:t=>({labelBytes:t("GET_LABEL_FILE_SIZE_BYTES")||void 0,labelKilobytes:t("GET_LABEL_FILE_SIZE_KILOBYTES")||void 0,labelMegabytes:t("GET_LABEL_FILE_SIZE_MEGABYTES")||void 0,labelGigabytes:t("GET_LABEL_FILE_SIZE_GIGABYTES")||void 0})}),Ks=e=>{const t=ge(e.items).length;if(!e.options.allowMultiple)return t===0;const n=e.options.maxFiles;return n===null||t<n},ar=(e,t,n)=>Math.max(Math.min(n,e),t),Js=(e,t,n)=>e.splice(t,0,n),ei=(e,t,n)=>Re(t)?null:typeof n>"u"?(e.push(t),t):(n=ar(n,0,e.length),Js(e,n,t),t),Lt=e=>/^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*)\s*$/i.test(e),je=e=>`${e}`.split("/").pop().split("?").shift(),ft=e=>e.split(".").pop(),ti=e=>{if(typeof e!="string")return"";const t=e.split("/").pop();return/svg/.test(t)?"svg":/zip|compressed/.test(t)?"zip":/plain/.test(t)?"txt":/msword/.test(t)?"doc":/[a-z]+/.test(t)?t==="jpeg"?"jpg":t:""},$e=(e,t="")=>(t+e).slice(-t.length),cr=(e=new Date)=>`${e.getFullYear()}-${$e(e.getMonth()+1,"00")}-${$e(e.getDate(),"00")}_${$e(e.getHours(),"00")}-${$e(e.getMinutes(),"00")}-${$e(e.getSeconds(),"00")}`,Ve=(e,t,n=null,r=null)=>{const s=typeof n=="string"?e.slice(0,e.size,n):e.slice(0,e.size,e.type);return s.lastModifiedDate=new Date,e._relativePath&&(s._relativePath=e._relativePath),ce(t)||(t=cr()),t&&r===null&&ft(t)?s.name=t:(r=r||ti(s.type),s.name=t+(r?"."+r:"")),s},ni=()=>window.BlobBuilder=window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder||window.MSBlobBuilder,ur=(e,t)=>{const n=ni();if(n){const r=new n;return r.append(e),r.getBlob(t)}return new Blob([e],{type:t})},ri=(e,t)=>{const n=new ArrayBuffer(e.length),r=new Uint8Array(n);for(let s=0;s<e.length;s++)r[s]=e.charCodeAt(s);return ur(n,t)},dr=e=>(/^data:(.+);/.exec(e)||[])[1]||null,si=e=>e.split(",")[1].replace(/\s/g,""),ii=e=>atob(si(e)),oi=e=>{const t=dr(e),n=ii(e);return ri(n,t)},li=(e,t,n)=>Ve(oi(e),t,null,n),ai=e=>{if(!/^content-disposition:/i.test(e))return null;const t=e.split(/filename=|filename\*=.+''/).splice(1).map(n=>n.trim().replace(/^["']|[;"']{0,2}$/g,"")).filter(n=>n.length);return t.length?decodeURI(t[t.length-1]):null},ci=e=>{if(/content-length:/i.test(e)){const t=e.match(/[0-9]+/)[0];return t?parseInt(t,10):null}return null},ui=e=>/x-content-transfer-id:/i.test(e)&&(e.split(":")[1]||"").trim()||null,Vt=e=>{const t={source:null,name:null,size:null},n=e.split(`
`);for(let r of n){const s=ai(r);if(s){t.name=s;continue}const o=ci(r);if(o){t.size=o;continue}const i=ui(r);if(i){t.source=i;continue}}return t},di=e=>{const t={source:null,complete:!1,progress:0,size:null,timestamp:null,duration:0,request:null},n=()=>t.progress,r=()=>{t.request&&t.request.abort&&t.request.abort()},s=()=>{const a=t.source;i.fire("init",a),a instanceof File?i.fire("load",a):a instanceof Blob?i.fire("load",Ve(a,a.name)):Lt(a)?i.fire("load",li(a)):o(a)},o=a=>{if(!e){i.fire("error",{type:"error",body:"Can't load URL",code:400});return}t.timestamp=Date.now(),t.request=e(a,l=>{t.duration=Date.now()-t.timestamp,t.complete=!0,l instanceof Blob&&(l=Ve(l,l.name||je(a))),i.fire("load",l instanceof Blob?l:l?l.body:null)},l=>{i.fire("error",typeof l=="string"?{type:"error",code:0,body:l}:l)},(l,u,c)=>{if(c&&(t.size=c),t.duration=Date.now()-t.timestamp,!l){t.progress=null;return}t.progress=u/c,i.fire("progress",t.progress)},()=>{i.fire("abort")},l=>{const u=Vt(typeof l=="string"?l:l.headers);i.fire("meta",{size:t.size||u.size,filename:u.name,source:u.source})})},i={...dt(),setSource:a=>t.source=a,getProgress:n,abort:r,load:s};return i},In=e=>/GET|HEAD/.test(e),Ce=(e,t,n)=>{const r={onheaders:()=>{},onprogress:()=>{},onload:()=>{},ontimeout:()=>{},onerror:()=>{},onabort:()=>{},abort:()=>{s=!0,i.abort()}};let s=!1,o=!1;n={method:"POST",headers:{},withCredentials:!1,...n},t=encodeURI(t),In(n.method)&&e&&(t=`${t}${encodeURIComponent(typeof e=="string"?e:JSON.stringify(e))}`);const i=new XMLHttpRequest,a=In(n.method)?i:i.upload;return a.onprogress=l=>{s||r.onprogress(l.lengthComputable,l.loaded,l.total)},i.onreadystatechange=()=>{i.readyState<2||i.readyState===4&&i.status===0||o||(o=!0,r.onheaders(i))},i.onload=()=>{i.status>=200&&i.status<300?r.onload(i):r.onerror(i)},i.onerror=()=>r.onerror(i),i.onabort=()=>{s=!0,r.onabort()},i.ontimeout=()=>r.ontimeout(i),i.open(n.method,t,!0),He(n.timeout)&&(i.timeout=n.timeout),Object.keys(n.headers).forEach(l=>{const u=unescape(encodeURIComponent(n.headers[l]));i.setRequestHeader(l,u)}),n.responseType&&(i.responseType=n.responseType),n.withCredentials&&(i.withCredentials=!0),i.send(e),r},J=(e,t,n,r)=>({type:e,code:t,body:n,headers:r}),ve=e=>t=>{e(J("error",0,"Timeout",t.getAllResponseHeaders()))},Tn=e=>/\?/.test(e),Xe=(...e)=>{let t="";return e.forEach(n=>{t+=Tn(t)&&Tn(n)?n.replace(/\?/,"&"):n}),t},gt=(e="",t)=>{if(typeof t=="function")return t;if(!t||!ce(t.url))return null;const n=t.onload||(s=>s),r=t.onerror||(s=>null);return(s,o,i,a,l,u)=>{const c=Ce(s,Xe(e,t.url),{...t,responseType:"blob"});return c.onload=E=>{const g=E.getAllResponseHeaders(),I=Vt(g).name||je(s);o(J("load",E.status,t.method==="HEAD"?null:Ve(n(E.response),I),g))},c.onerror=E=>{i(J("error",E.status,r(E.response)||E.statusText,E.getAllResponseHeaders()))},c.onheaders=E=>{u(J("headers",E.status,null,E.getAllResponseHeaders()))},c.ontimeout=ve(i),c.onprogress=a,c.onabort=l,c}},pe={QUEUED:0,COMPLETE:1,PROCESSING:2,ERROR:3,WAITING:4},fi=(e,t,n,r,s,o,i,a,l,u,c)=>{const E=[],{chunkTransferId:g,chunkServer:I,chunkSize:f,chunkRetryDelays:m}=c,P={serverId:g,aborted:!1},S=t.ondata||(y=>y),b=t.onload||((y,V)=>V==="HEAD"?y.getResponseHeader("Upload-Offset"):y.response),C=t.onerror||(y=>null),M=y=>{const V=new FormData;ie(s)&&V.append(n,JSON.stringify(s));const p=typeof t.headers=="function"?t.headers(r,s):{...t.headers,"Upload-Length":r.size},D={...t,headers:p},G=Ce(S(V),Xe(e,t.url),D);G.onload=v=>y(b(v,D.method)),G.onerror=v=>i(J("error",v.status,C(v.response)||v.statusText,v.getAllResponseHeaders())),G.ontimeout=ve(i)},w=y=>{const V=Xe(e,I.url,P.serverId),D={headers:typeof t.headers=="function"?t.headers(P.serverId):{...t.headers},method:"HEAD"},G=Ce(null,V,D);G.onload=v=>y(b(v,D.method)),G.onerror=v=>i(J("error",v.status,C(v.response)||v.statusText,v.getAllResponseHeaders())),G.ontimeout=ve(i)},F=Math.floor(r.size/f);for(let y=0;y<=F;y++){const V=y*f,p=r.slice(V,V+f,"application/offset+octet-stream");E[y]={index:y,size:p.size,offset:V,data:p,file:r,progress:0,retries:[...m],status:pe.QUEUED,error:null,request:null,timeout:null}}const W=()=>o(P.serverId),A=y=>y.status===pe.QUEUED||y.status===pe.ERROR,B=y=>{if(P.aborted)return;if(y=y||E.find(A),!y){E.every(z=>z.status===pe.COMPLETE)&&W();return}y.status=pe.PROCESSING,y.progress=null;const V=I.ondata||(z=>z),p=I.onerror||(z=>null),D=Xe(e,I.url,P.serverId),G=typeof I.headers=="function"?I.headers(y):{...I.headers,"Content-Type":"application/offset+octet-stream","Upload-Offset":y.offset,"Upload-Length":r.size,"Upload-Name":r.name},v=y.request=Ce(V(y.data),D,{...I,headers:G});v.onload=()=>{y.status=pe.COMPLETE,y.request=null,U()},v.onprogress=(z,k,Se)=>{y.progress=z?k:null,N()},v.onerror=z=>{y.status=pe.ERROR,y.request=null,y.error=p(z.response)||z.statusText,Y(y)||i(J("error",z.status,p(z.response)||z.statusText,z.getAllResponseHeaders()))},v.ontimeout=z=>{y.status=pe.ERROR,y.request=null,Y(y)||ve(i)(z)},v.onabort=()=>{y.status=pe.QUEUED,y.request=null,l()}},Y=y=>y.retries.length===0?!1:(y.status=pe.WAITING,clearTimeout(y.timeout),y.timeout=setTimeout(()=>{B(y)},y.retries.shift()),!0),N=()=>{const y=E.reduce((p,D)=>p===null||D.progress===null?null:p+D.progress,0);if(y===null)return a(!1,0,0);const V=E.reduce((p,D)=>p+D.size,0);a(!0,y,V)},U=()=>{E.filter(V=>V.status===pe.PROCESSING).length>=1||B()},X=()=>{E.forEach(y=>{clearTimeout(y.timeout),y.request&&y.request.abort()})};return P.serverId?w(y=>{P.aborted||(E.filter(V=>V.offset<y).forEach(V=>{V.status=pe.COMPLETE,V.progress=V.size}),U())}):M(y=>{P.aborted||(u(y),P.serverId=y,U())}),{abort:()=>{P.aborted=!0,X()}}},Ei=(e,t,n,r)=>(s,o,i,a,l,u,c)=>{if(!s)return;const E=r.chunkUploads,g=E&&s.size>r.chunkSize,I=E&&(g||r.chunkForce);if(s instanceof Blob&&I)return fi(e,t,n,s,o,i,a,l,u,c,r);const f=t.ondata||(w=>w),m=t.onload||(w=>w),P=t.onerror||(w=>null),S=typeof t.headers=="function"?t.headers(s,o)||{}:{...t.headers},b={...t,headers:S};var C=new FormData;ie(o)&&C.append(n,JSON.stringify(o)),(s instanceof Blob?[{name:null,file:s}]:s).forEach(w=>{C.append(n,w.file,w.name===null?w.file.name:`${w.name}${w.file.name}`)});const M=Ce(f(C),Xe(e,t.url),b);return M.onload=w=>{i(J("load",w.status,m(w.response),w.getAllResponseHeaders()))},M.onerror=w=>{a(J("error",w.status,P(w.response)||w.statusText,w.getAllResponseHeaders()))},M.ontimeout=ve(a),M.onprogress=l,M.onabort=u,M},pi=(e="",t,n,r)=>typeof t=="function"?(...s)=>t(n,...s,r):!t||!ce(t.url)?null:Ei(e,t,n,r),We=(e="",t)=>{if(typeof t=="function")return t;if(!t||!ce(t.url))return(s,o)=>o();const n=t.onload||(s=>s),r=t.onerror||(s=>null);return(s,o,i)=>{const a=Ce(s,e+t.url,t);return a.onload=l=>{o(J("load",l.status,n(l.response),l.getAllResponseHeaders()))},a.onerror=l=>{i(J("error",l.status,r(l.response)||l.statusText,l.getAllResponseHeaders()))},a.ontimeout=ve(i),a}},fr=(e=0,t=1)=>e+Math.random()*(t-e),_i=(e,t=1e3,n=0,r=25,s=250)=>{let o=null;const i=Date.now(),a=()=>{let l=Date.now()-i,u=fr(r,s);l+u>t&&(u=l+u-t);let c=l/t;if(c>=1||document.hidden){e(1);return}e(c),o=setTimeout(a,u)};return t>0&&a(),{clear:()=>{clearTimeout(o)}}},Ii=(e,t)=>{const n={complete:!1,perceivedProgress:0,perceivedPerformanceUpdater:null,progress:null,timestamp:null,perceivedDuration:0,duration:0,request:null,response:null},{allowMinimumUploadDuration:r}=t,s=(c,E)=>{const g=()=>{n.duration===0||n.progress===null||u.fire("progress",u.getProgress())},I=()=>{n.complete=!0,u.fire("load-perceived",n.response.body)};u.fire("start"),n.timestamp=Date.now(),n.perceivedPerformanceUpdater=_i(f=>{n.perceivedProgress=f,n.perceivedDuration=Date.now()-n.timestamp,g(),n.response&&n.perceivedProgress===1&&!n.complete&&I()},r?fr(750,1500):0),n.request=e(c,E,f=>{n.response=ie(f)?f:{type:"load",code:200,body:`${f}`,headers:{}},n.duration=Date.now()-n.timestamp,n.progress=1,u.fire("load",n.response.body),(!r||r&&n.perceivedProgress===1)&&I()},f=>{n.perceivedPerformanceUpdater.clear(),u.fire("error",ie(f)?f:{type:"error",code:0,body:`${f}`})},(f,m,P)=>{n.duration=Date.now()-n.timestamp,n.progress=f?m/P:null,g()},()=>{n.perceivedPerformanceUpdater.clear(),u.fire("abort",n.response?n.response.body:null)},f=>{u.fire("transfer",f)})},o=()=>{n.request&&(n.perceivedPerformanceUpdater.clear(),n.request.abort&&n.request.abort(),n.complete=!0)},i=()=>{o(),n.complete=!1,n.perceivedProgress=0,n.progress=0,n.timestamp=null,n.perceivedDuration=0,n.duration=0,n.request=null,n.response=null},a=r?()=>n.progress?Math.min(n.progress,n.perceivedProgress):null:()=>n.progress||null,l=r?()=>Math.min(n.duration,n.perceivedDuration):()=>n.duration,u={...dt(),process:s,abort:o,getProgress:a,getDuration:l,reset:i};return u},Er=e=>e.substring(0,e.lastIndexOf("."))||e,Ti=e=>{let t=[e.name,e.size,e.type];return e instanceof Blob||Lt(e)?t[0]=e.name||cr():Lt(e)?(t[1]=e.length,t[2]=dr(e)):ce(e)&&(t[0]=je(e),t[1]=0,t[2]="application/octet-stream"),{name:t[0],size:t[1],type:t[2]}},qe=e=>!!(e instanceof File||e instanceof Blob&&e.name),pr=e=>{if(!ie(e))return e;const t=at(e)?[]:{};for(const n in e){if(!e.hasOwnProperty(n))continue;const r=e[n];t[n]=r&&ie(r)?pr(r):r}return t},mi=(e=null,t=null,n=null)=>{const r=Bt(),s={archived:!1,frozen:!1,released:!1,source:null,file:n,serverFileReference:t,transferId:null,processingAborted:!1,status:t?x.PROCESSING_COMPLETE:x.INIT,activeLoader:null,activeProcessor:null};let o=null;const i={},a=A=>s.status=A,l=(A,...B)=>{s.released||s.frozen||F.fire(A,...B)},u=()=>ft(s.file.name),c=()=>s.file.type,E=()=>s.file.size,g=()=>s.file,I=(A,B,Y)=>{if(s.source=A,F.fireSync("init"),s.file){F.fireSync("load-skip");return}s.file=Ti(A),B.on("init",()=>{l("load-init")}),B.on("meta",N=>{s.file.size=N.size,s.file.filename=N.filename,N.source&&(e=se.LIMBO,s.serverFileReference=N.source,s.status=x.PROCESSING_COMPLETE),l("load-meta")}),B.on("progress",N=>{a(x.LOADING),l("load-progress",N)}),B.on("error",N=>{a(x.LOAD_ERROR),l("load-request-error",N)}),B.on("abort",()=>{a(x.INIT),l("load-abort")}),B.on("load",N=>{s.activeLoader=null;const U=y=>{s.file=qe(y)?y:s.file,e===se.LIMBO&&s.serverFileReference?a(x.PROCESSING_COMPLETE):a(x.IDLE),l("load")},X=y=>{s.file=N,l("load-meta"),a(x.LOAD_ERROR),l("load-file-error",y)};if(s.serverFileReference){U(N);return}Y(N,U,X)}),B.setSource(A),s.activeLoader=B,B.load()},f=()=>{s.activeLoader&&s.activeLoader.load()},m=()=>{if(s.activeLoader){s.activeLoader.abort();return}a(x.INIT),l("load-abort")},P=(A,B)=>{if(s.processingAborted){s.processingAborted=!1;return}if(a(x.PROCESSING),o=null,!(s.file instanceof Blob)){F.on("load",()=>{P(A,B)});return}A.on("load",U=>{s.transferId=null,s.serverFileReference=U}),A.on("transfer",U=>{s.transferId=U}),A.on("load-perceived",U=>{s.activeProcessor=null,s.transferId=null,s.serverFileReference=U,a(x.PROCESSING_COMPLETE),l("process-complete",U)}),A.on("start",()=>{l("process-start")}),A.on("error",U=>{s.activeProcessor=null,a(x.PROCESSING_ERROR),l("process-error",U)}),A.on("abort",U=>{s.activeProcessor=null,s.serverFileReference=U,a(x.IDLE),l("process-abort"),o&&o()}),A.on("progress",U=>{l("process-progress",U)});const Y=U=>{s.archived||A.process(U,{...i})},N=console.error;B(s.file,Y,N),s.activeProcessor=A},S=()=>{s.processingAborted=!1,a(x.PROCESSING_QUEUED)},b=()=>new Promise(A=>{if(!s.activeProcessor){s.processingAborted=!0,a(x.IDLE),l("process-abort"),A();return}o=()=>{A()},s.activeProcessor.abort()}),C=(A,B)=>new Promise((Y,N)=>{const U=s.serverFileReference!==null?s.serverFileReference:s.transferId;if(U===null){Y();return}A(U,()=>{s.serverFileReference=null,s.transferId=null,Y()},X=>{if(!B){Y();return}a(x.PROCESSING_REVERT_ERROR),l("process-revert-error"),N(X)}),a(x.IDLE),l("process-revert")}),M=(A,B,Y)=>{const N=A.split("."),U=N[0],X=N.pop();let y=i;N.forEach(V=>y=y[V]),JSON.stringify(y[X])!==JSON.stringify(B)&&(y[X]=B,l("metadata-update",{key:U,value:i[U],silent:Y}))},F={id:{get:()=>r},origin:{get:()=>e,set:A=>e=A},serverId:{get:()=>s.serverFileReference},transferId:{get:()=>s.transferId},status:{get:()=>s.status},filename:{get:()=>s.file.name},filenameWithoutExtension:{get:()=>Er(s.file.name)},fileExtension:{get:u},fileType:{get:c},fileSize:{get:E},file:{get:g},relativePath:{get:()=>s.file._relativePath},source:{get:()=>s.source},getMetadata:A=>pr(A?i[A]:i),setMetadata:(A,B,Y)=>{if(ie(A)){const N=A;return Object.keys(N).forEach(U=>{M(U,N[U],B)}),A}return M(A,B,Y),B},extend:(A,B)=>W[A]=B,abortLoad:m,retryLoad:f,requestProcessing:S,abortProcessing:b,load:I,process:P,revert:C,...dt(),freeze:()=>s.frozen=!0,release:()=>s.released=!0,released:{get:()=>s.released},archive:()=>s.archived=!0,archived:{get:()=>s.archived}},W=Le(F);return W},gi=(e,t)=>Re(t)?0:ce(t)?e.findIndex(n=>n.id===t):-1,mn=(e,t)=>{const n=gi(e,t);if(!(n<0))return e[n]||null},gn=(e,t,n,r,s,o)=>{const i=Ce(null,e,{method:"GET",responseType:"blob"});return i.onload=a=>{const l=a.getAllResponseHeaders(),u=Vt(l).name||je(e);t(J("load",a.status,Ve(a.response,u),l))},i.onerror=a=>{n(J("error",a.status,a.statusText,a.getAllResponseHeaders()))},i.onheaders=a=>{o(J("headers",a.status,null,a.getAllResponseHeaders()))},i.ontimeout=ve(n),i.onprogress=r,i.onabort=s,i},hn=e=>(e.indexOf("//")===0&&(e=location.protocol+e),e.toLowerCase().replace("blob:","").replace(/([a-z])?:\/\//,"$1").split("/")[0]),hi=e=>(e.indexOf(":")>-1||e.indexOf("//")>-1)&&hn(location.href)!==hn(e),Ze=e=>(...t)=>Me(e)?e(...t):e,Ri=e=>!qe(e.file),ht=(e,t)=>{clearTimeout(t.listUpdateTimeout),t.listUpdateTimeout=setTimeout(()=>{e("DID_UPDATE_ITEMS",{items:ge(t.items)})},0)},Rn=(e,...t)=>new Promise(n=>{if(!e)return n(!0);const r=e(...t);if(r==null)return n(!0);if(typeof r=="boolean")return n(r);typeof r.then=="function"&&r.then(n)}),Rt=(e,t)=>{e.items.sort((n,r)=>t(de(n),de(r)))},_e=(e,t)=>({query:n,success:r=()=>{},failure:s=()=>{},...o}={})=>{const i=we(e.items,n);if(!i){s({error:J("error",0,"Item not found"),file:null});return}t(i,r,s,o||{})},Oi=(e,t,n)=>({ABORT_ALL:()=>{ge(n.items).forEach(r=>{r.freeze(),r.abortLoad(),r.abortProcessing()})},DID_SET_FILES:({value:r=[]})=>{const s=r.map(i=>({source:i.source?i.source:i,options:i.options}));let o=ge(n.items);o.forEach(i=>{s.find(a=>a.source===i.source||a.source===i.file)||e("REMOVE_ITEM",{query:i,remove:!1})}),o=ge(n.items),s.forEach((i,a)=>{o.find(l=>l.source===i.source||l.file===i.source)||e("ADD_ITEM",{...i,interactionMethod:Ie.NONE,index:a})})},DID_UPDATE_ITEM_METADATA:({id:r,action:s,change:o})=>{o.silent||(clearTimeout(n.itemUpdateTimeout),n.itemUpdateTimeout=setTimeout(()=>{const i=mn(n.items,r);if(!t("IS_ASYNC")){me("SHOULD_PREPARE_OUTPUT",!1,{item:i,query:t,action:s,change:o}).then(c=>{const E=t("GET_BEFORE_PREPARE_FILE");E&&(c=E(i,c)),c&&e("REQUEST_PREPARE_OUTPUT",{query:r,item:i,success:g=>{e("DID_PREPARE_OUTPUT",{id:r,file:g})}},!0)});return}i.origin===se.LOCAL&&e("DID_LOAD_ITEM",{id:i.id,error:null,serverFileReference:i.source});const a=()=>{setTimeout(()=>{e("REQUEST_ITEM_PROCESSING",{query:r})},32)},l=c=>{i.revert(We(n.options.server.url,n.options.server.revert),t("GET_FORCE_REVERT")).then(c?a:()=>{}).catch(()=>{})},u=c=>{i.abortProcessing().then(c?a:()=>{})};if(i.status===x.PROCESSING_COMPLETE)return l(n.options.instantUpload);if(i.status===x.PROCESSING)return u(n.options.instantUpload);n.options.instantUpload&&a()},0))},MOVE_ITEM:({query:r,index:s})=>{const o=we(n.items,r);if(!o)return;const i=n.items.indexOf(o);s=ar(s,0,n.items.length-1),i!==s&&n.items.splice(s,0,n.items.splice(i,1)[0])},SORT:({compare:r})=>{Rt(n,r),e("DID_SORT_ITEMS",{items:t("GET_ACTIVE_ITEMS")})},ADD_ITEMS:({items:r,index:s,interactionMethod:o,success:i=()=>{},failure:a=()=>{}})=>{let l=s;if(s===-1||typeof s>"u"){const I=t("GET_ITEM_INSERT_LOCATION"),f=t("GET_TOTAL_ITEMS");l=I==="before"?0:f}const u=t("GET_IGNORED_FILES"),c=I=>qe(I)?!u.includes(I.name.toLowerCase()):!Re(I),g=r.filter(c).map(I=>new Promise((f,m)=>{e("ADD_ITEM",{interactionMethod:o,source:I.source||I,success:f,failure:m,index:l++,options:I.options||{}})}));Promise.all(g).then(i).catch(a)},ADD_ITEM:({source:r,index:s=-1,interactionMethod:o,success:i=()=>{},failure:a=()=>{},options:l={}})=>{if(Re(r)){a({error:J("error",0,"No source"),file:null});return}if(qe(r)&&n.options.ignoredFiles.includes(r.name.toLowerCase()))return;if(!Ks(n)){if(n.options.allowMultiple||!n.options.allowMultiple&&!n.options.allowReplace){const b=J("warning",0,"Max files");e("DID_THROW_MAX_FILES",{source:r,error:b}),a({error:b,file:null});return}const S=ge(n.items)[0];if(S.status===x.PROCESSING_COMPLETE||S.status===x.PROCESSING_REVERT_ERROR){const b=t("GET_FORCE_REVERT");if(S.revert(We(n.options.server.url,n.options.server.revert),b).then(()=>{b&&e("ADD_ITEM",{source:r,index:s,interactionMethod:o,success:i,failure:a,options:l})}).catch(()=>{}),b)return}e("REMOVE_ITEM",{query:S.id})}const u=l.type==="local"?se.LOCAL:l.type==="limbo"?se.LIMBO:se.INPUT,c=mi(u,u===se.INPUT?null:r,l.file);Object.keys(l.metadata||{}).forEach(S=>{c.setMetadata(S,l.metadata[S])}),Ge("DID_CREATE_ITEM",c,{query:t,dispatch:e});const E=t("GET_ITEM_INSERT_LOCATION");n.options.itemInsertLocationFreedom||(s=E==="before"?-1:n.items.length),ei(n.items,c,s),Me(E)&&r&&Rt(n,E);const g=c.id;c.on("init",()=>{e("DID_INIT_ITEM",{id:g})}),c.on("load-init",()=>{e("DID_START_ITEM_LOAD",{id:g})}),c.on("load-meta",()=>{e("DID_UPDATE_ITEM_META",{id:g})}),c.on("load-progress",S=>{e("DID_UPDATE_ITEM_LOAD_PROGRESS",{id:g,progress:S})}),c.on("load-request-error",S=>{const b=Ze(n.options.labelFileLoadError)(S);if(S.code>=400&&S.code<500){e("DID_THROW_ITEM_INVALID",{id:g,error:S,status:{main:b,sub:`${S.code} (${S.body})`}}),a({error:S,file:de(c)});return}e("DID_THROW_ITEM_LOAD_ERROR",{id:g,error:S,status:{main:b,sub:n.options.labelTapToRetry}})}),c.on("load-file-error",S=>{e("DID_THROW_ITEM_INVALID",{id:g,error:S.status,status:S.status}),a({error:S.status,file:de(c)})}),c.on("load-abort",()=>{e("REMOVE_ITEM",{query:g})}),c.on("load-skip",()=>{e("COMPLETE_LOAD_ITEM",{query:g,item:c,data:{source:r,success:i}})}),c.on("load",()=>{const S=b=>{if(!b){e("REMOVE_ITEM",{query:g});return}c.on("metadata-update",C=>{e("DID_UPDATE_ITEM_METADATA",{id:g,change:C})}),me("SHOULD_PREPARE_OUTPUT",!1,{item:c,query:t}).then(C=>{const M=t("GET_BEFORE_PREPARE_FILE");M&&(C=M(c,C));const w=()=>{e("COMPLETE_LOAD_ITEM",{query:g,item:c,data:{source:r,success:i}}),ht(e,n)};if(C){e("REQUEST_PREPARE_OUTPUT",{query:g,item:c,success:F=>{e("DID_PREPARE_OUTPUT",{id:g,file:F}),w()}},!0);return}w()})};me("DID_LOAD_ITEM",c,{query:t,dispatch:e}).then(()=>{Rn(t("GET_BEFORE_ADD_FILE"),de(c)).then(S)}).catch(b=>{if(!b||!b.error||!b.status)return S(!1);e("DID_THROW_ITEM_INVALID",{id:g,error:b.error,status:b.status})})}),c.on("process-start",()=>{e("DID_START_ITEM_PROCESSING",{id:g})}),c.on("process-progress",S=>{e("DID_UPDATE_ITEM_PROCESS_PROGRESS",{id:g,progress:S})}),c.on("process-error",S=>{e("DID_THROW_ITEM_PROCESSING_ERROR",{id:g,error:S,status:{main:Ze(n.options.labelFileProcessingError)(S),sub:n.options.labelTapToRetry}})}),c.on("process-revert-error",S=>{e("DID_THROW_ITEM_PROCESSING_REVERT_ERROR",{id:g,error:S,status:{main:Ze(n.options.labelFileProcessingRevertError)(S),sub:n.options.labelTapToRetry}})}),c.on("process-complete",S=>{e("DID_COMPLETE_ITEM_PROCESSING",{id:g,error:null,serverFileReference:S}),e("DID_DEFINE_VALUE",{id:g,value:S})}),c.on("process-abort",()=>{e("DID_ABORT_ITEM_PROCESSING",{id:g})}),c.on("process-revert",()=>{e("DID_REVERT_ITEM_PROCESSING",{id:g}),e("DID_DEFINE_VALUE",{id:g,value:null})}),e("DID_ADD_ITEM",{id:g,index:s,interactionMethod:o}),ht(e,n);const{url:I,load:f,restore:m,fetch:P}=n.options.server||{};c.load(r,di(u===se.INPUT?ce(r)&&hi(r)&&P?gt(I,P):gn:u===se.LIMBO?gt(I,m):gt(I,f)),(S,b,C)=>{me("LOAD_FILE",S,{query:t}).then(b).catch(C)})},REQUEST_PREPARE_OUTPUT:({item:r,success:s,failure:o=()=>{}})=>{const i={error:J("error",0,"Item not found"),file:null};if(r.archived)return o(i);me("PREPARE_OUTPUT",r.file,{query:t,item:r}).then(a=>{me("COMPLETE_PREPARE_OUTPUT",a,{query:t,item:r}).then(l=>{if(r.archived)return o(i);s(l)})})},COMPLETE_LOAD_ITEM:({item:r,data:s})=>{const{success:o,source:i}=s,a=t("GET_ITEM_INSERT_LOCATION");if(Me(a)&&i&&Rt(n,a),e("DID_LOAD_ITEM",{id:r.id,error:null,serverFileReference:r.origin===se.INPUT?null:i}),o(de(r)),r.origin===se.LOCAL){e("DID_LOAD_LOCAL_ITEM",{id:r.id});return}if(r.origin===se.LIMBO){e("DID_COMPLETE_ITEM_PROCESSING",{id:r.id,error:null,serverFileReference:i}),e("DID_DEFINE_VALUE",{id:r.id,value:r.serverId||i});return}t("IS_ASYNC")&&n.options.instantUpload&&e("REQUEST_ITEM_PROCESSING",{query:r.id})},RETRY_ITEM_LOAD:_e(n,r=>{r.retryLoad()}),REQUEST_ITEM_PREPARE:_e(n,(r,s,o)=>{e("REQUEST_PREPARE_OUTPUT",{query:r.id,item:r,success:i=>{e("DID_PREPARE_OUTPUT",{id:r.id,file:i}),s({file:r,output:i})},failure:o},!0)}),REQUEST_ITEM_PROCESSING:_e(n,(r,s,o)=>{if(!(r.status===x.IDLE||r.status===x.PROCESSING_ERROR)){const a=()=>e("REQUEST_ITEM_PROCESSING",{query:r,success:s,failure:o}),l=()=>document.hidden?a():setTimeout(a,32);r.status===x.PROCESSING_COMPLETE||r.status===x.PROCESSING_REVERT_ERROR?r.revert(We(n.options.server.url,n.options.server.revert),t("GET_FORCE_REVERT")).then(l).catch(()=>{}):r.status===x.PROCESSING&&r.abortProcessing().then(l);return}r.status!==x.PROCESSING_QUEUED&&(r.requestProcessing(),e("DID_REQUEST_ITEM_PROCESSING",{id:r.id}),e("PROCESS_ITEM",{query:r,success:s,failure:o},!0))}),PROCESS_ITEM:_e(n,(r,s,o)=>{const i=t("GET_MAX_PARALLEL_UPLOADS");if(t("GET_ITEMS_BY_STATUS",x.PROCESSING).length===i){n.processingQueue.push({id:r.id,success:s,failure:o});return}if(r.status===x.PROCESSING)return;const l=()=>{const c=n.processingQueue.shift();if(!c)return;const{id:E,success:g,failure:I}=c,f=we(n.items,E);if(!f||f.archived){l();return}e("PROCESS_ITEM",{query:E,success:g,failure:I},!0)};r.onOnce("process-complete",()=>{s(de(r)),l();const c=n.options.server;if(n.options.instantUpload&&r.origin===se.LOCAL&&Me(c.remove)){const I=()=>{};r.origin=se.LIMBO,n.options.server.remove(r.source,I,I)}t("GET_ITEMS_BY_STATUS",x.PROCESSING_COMPLETE).length===n.items.length&&e("DID_COMPLETE_ITEM_PROCESSING_ALL")}),r.onOnce("process-error",c=>{o({error:c,file:de(r)}),l()});const u=n.options;r.process(Ii(pi(u.server.url,u.server.process,u.name,{chunkTransferId:r.transferId,chunkServer:u.server.patch,chunkUploads:u.chunkUploads,chunkForce:u.chunkForce,chunkSize:u.chunkSize,chunkRetryDelays:u.chunkRetryDelays}),{allowMinimumUploadDuration:t("GET_ALLOW_MINIMUM_UPLOAD_DURATION")}),(c,E,g)=>{me("PREPARE_OUTPUT",c,{query:t,item:r}).then(I=>{e("DID_PREPARE_OUTPUT",{id:r.id,file:I}),E(I)}).catch(g)})}),RETRY_ITEM_PROCESSING:_e(n,r=>{e("REQUEST_ITEM_PROCESSING",{query:r})}),REQUEST_REMOVE_ITEM:_e(n,r=>{Rn(t("GET_BEFORE_REMOVE_FILE"),de(r)).then(s=>{s&&e("REMOVE_ITEM",{query:r})})}),RELEASE_ITEM:_e(n,r=>{r.release()}),REMOVE_ITEM:_e(n,(r,s,o,i)=>{const a=()=>{const u=r.id;mn(n.items,u).archive(),e("DID_REMOVE_ITEM",{error:null,id:u,item:r}),ht(e,n),s(de(r))},l=n.options.server;r.origin===se.LOCAL&&l&&Me(l.remove)&&i.remove!==!1?(e("DID_START_ITEM_REMOVE",{id:r.id}),l.remove(r.source,()=>a(),u=>{e("DID_THROW_ITEM_REMOVE_ERROR",{id:r.id,error:J("error",0,u,null),status:{main:Ze(n.options.labelFileRemoveError)(u),sub:n.options.labelTapToRetry}})})):((i.revert&&r.origin!==se.LOCAL&&r.serverId!==null||n.options.chunkUploads&&r.file.size>n.options.chunkSize||n.options.chunkUploads&&n.options.chunkForce)&&r.revert(We(n.options.server.url,n.options.server.revert),t("GET_FORCE_REVERT")),a())}),ABORT_ITEM_LOAD:_e(n,r=>{r.abortLoad()}),ABORT_ITEM_PROCESSING:_e(n,r=>{if(r.serverId){e("REVERT_ITEM_PROCESSING",{id:r.id});return}r.abortProcessing().then(()=>{n.options.instantUpload&&e("REMOVE_ITEM",{query:r.id})})}),REQUEST_REVERT_ITEM_PROCESSING:_e(n,r=>{if(!n.options.instantUpload){e("REVERT_ITEM_PROCESSING",{query:r});return}const s=a=>{a&&e("REVERT_ITEM_PROCESSING",{query:r})},o=t("GET_BEFORE_REMOVE_FILE");if(!o)return s(!0);const i=o(de(r));if(i==null)return s(!0);if(typeof i=="boolean")return s(i);typeof i.then=="function"&&i.then(s)}),REVERT_ITEM_PROCESSING:_e(n,r=>{r.revert(We(n.options.server.url,n.options.server.revert),t("GET_FORCE_REVERT")).then(()=>{(n.options.instantUpload||Ri(r))&&e("REMOVE_ITEM",{query:r.id})}).catch(()=>{})}),SET_OPTIONS:({options:r})=>{const s=Object.keys(r),o=Si.filter(a=>s.includes(a));[...o,...Object.keys(r).filter(a=>!o.includes(a))].forEach(a=>{e(`SET_${ut(a,"_").toUpperCase()}`,{value:r[a]})})}}),Si=["server"],qt=e=>e,Oe=e=>document.createElement(e),ee=(e,t)=>{let n=e.childNodes[0];n?t!==n.nodeValue&&(n.nodeValue=t):(n=document.createTextNode(t),e.appendChild(n))},On=(e,t,n,r)=>{const s=(r%360-90)*Math.PI/180;return{x:e+n*Math.cos(s),y:t+n*Math.sin(s)}},Di=(e,t,n,r,s,o)=>{const i=On(e,t,n,s),a=On(e,t,n,r);return["M",i.x,i.y,"A",n,n,0,o,0,a.x,a.y].join(" ")},yi=(e,t,n,r,s)=>{let o=1;return s>r&&s-r<=.5&&(o=0),r>s&&r-s>=.5&&(o=0),Di(e,t,n,Math.min(.9999,r)*360,Math.min(.9999,s)*360,o)},Ai=({root:e,props:t})=>{t.spin=!1,t.progress=0,t.opacity=0;const n=st("svg");e.ref.path=st("path",{"stroke-width":2,"stroke-linecap":"round"}),n.appendChild(e.ref.path),e.ref.svg=n,e.appendChild(n)},Li=({root:e,props:t})=>{if(t.opacity===0)return;t.align&&(e.element.dataset.align=t.align);const n=parseInt(te(e.ref.path,"stroke-width"),10),r=e.rect.element.width*.5;let s=0,o=0;t.spin?(s=0,o=.5):(s=0,o=t.progress);const i=yi(r,r,r-n,s,o);te(e.ref.path,"d",i),te(e.ref.path,"stroke-opacity",t.spin||t.progress>0?1:0)},Sn=ne({tag:"div",name:"progress-indicator",ignoreRectUpdate:!0,ignoreRect:!0,create:Ai,write:Li,mixins:{apis:["progress","spin","align"],styles:["opacity"],animations:{opacity:{type:"tween",duration:500},progress:{type:"spring",stiffness:.95,damping:.65,mass:10}}}}),bi=({root:e,props:t})=>{e.element.innerHTML=(t.icon||"")+`<span>${t.label}</span>`,t.isDisabled=!1},Pi=({root:e,props:t})=>{const{isDisabled:n}=t,r=e.query("GET_DISABLED")||t.opacity===0;r&&!n?(t.isDisabled=!0,te(e.element,"disabled","disabled")):!r&&n&&(t.isDisabled=!1,e.element.removeAttribute("disabled"))},_r=ne({tag:"button",attributes:{type:"button"},ignoreRect:!0,ignoreRectUpdate:!0,name:"file-action-button",mixins:{apis:["label"],styles:["translateX","translateY","scaleX","scaleY","opacity"],animations:{scaleX:"spring",scaleY:"spring",translateX:"spring",translateY:"spring",opacity:{type:"tween",duration:250}},listeners:!0},create:bi,write:Pi}),Ir=(e,t=".",n=1e3,r={})=>{const{labelBytes:s="bytes",labelKilobytes:o="KB",labelMegabytes:i="MB",labelGigabytes:a="GB"}=r;e=Math.round(Math.abs(e));const l=n,u=n*n,c=n*n*n;return e<l?`${e} ${s}`:e<u?`${Math.floor(e/l)} ${o}`:e<c?`${Dn(e/u,1,t)} ${i}`:`${Dn(e/c,2,t)} ${a}`},Dn=(e,t,n)=>e.toFixed(t).split(".").filter(r=>r!=="0").join(n),Mi=({root:e,props:t})=>{const n=Oe("span");n.className="filepond--file-info-main",te(n,"aria-hidden","true"),e.appendChild(n),e.ref.fileName=n;const r=Oe("span");r.className="filepond--file-info-sub",e.appendChild(r),e.ref.fileSize=r,ee(r,e.query("GET_LABEL_FILE_WAITING_FOR_SIZE")),ee(n,qt(e.query("GET_ITEM_NAME",t.id)))},bt=({root:e,props:t})=>{ee(e.ref.fileSize,Ir(e.query("GET_ITEM_SIZE",t.id),".",e.query("GET_FILE_SIZE_BASE"),e.query("GET_FILE_SIZE_LABELS",e.query))),ee(e.ref.fileName,qt(e.query("GET_ITEM_NAME",t.id)))},yn=({root:e,props:t})=>{if(He(e.query("GET_ITEM_SIZE",t.id))){bt({root:e,props:t});return}ee(e.ref.fileSize,e.query("GET_LABEL_FILE_SIZE_NOT_AVAILABLE"))},wi=ne({name:"file-info",ignoreRect:!0,ignoreRectUpdate:!0,write:ue({DID_LOAD_ITEM:bt,DID_UPDATE_ITEM_META:bt,DID_THROW_ITEM_LOAD_ERROR:yn,DID_THROW_ITEM_INVALID:yn}),didCreateView:e=>{Ge("CREATE_VIEW",{...e,view:e})},create:Mi,mixins:{styles:["translateX","translateY"],animations:{translateX:"spring",translateY:"spring"}}}),Tr=e=>Math.round(e*100),Ci=({root:e})=>{const t=Oe("span");t.className="filepond--file-status-main",e.appendChild(t),e.ref.main=t;const n=Oe("span");n.className="filepond--file-status-sub",e.appendChild(n),e.ref.sub=n,mr({root:e,action:{progress:null}})},mr=({root:e,action:t})=>{const n=t.progress===null?e.query("GET_LABEL_FILE_LOADING"):`${e.query("GET_LABEL_FILE_LOADING")} ${Tr(t.progress)}%`;ee(e.ref.main,n),ee(e.ref.sub,e.query("GET_LABEL_TAP_TO_CANCEL"))},vi=({root:e,action:t})=>{const n=t.progress===null?e.query("GET_LABEL_FILE_PROCESSING"):`${e.query("GET_LABEL_FILE_PROCESSING")} ${Tr(t.progress)}%`;ee(e.ref.main,n),ee(e.ref.sub,e.query("GET_LABEL_TAP_TO_CANCEL"))},Ni=({root:e})=>{ee(e.ref.main,e.query("GET_LABEL_FILE_PROCESSING")),ee(e.ref.sub,e.query("GET_LABEL_TAP_TO_CANCEL"))},Gi=({root:e})=>{ee(e.ref.main,e.query("GET_LABEL_FILE_PROCESSING_ABORTED")),ee(e.ref.sub,e.query("GET_LABEL_TAP_TO_RETRY"))},Fi=({root:e})=>{ee(e.ref.main,e.query("GET_LABEL_FILE_PROCESSING_COMPLETE")),ee(e.ref.sub,e.query("GET_LABEL_TAP_TO_UNDO"))},An=({root:e})=>{ee(e.ref.main,""),ee(e.ref.sub,"")},ke=({root:e,action:t})=>{ee(e.ref.main,t.status.main),ee(e.ref.sub,t.status.sub)},Bi=ne({name:"file-status",ignoreRect:!0,ignoreRectUpdate:!0,write:ue({DID_LOAD_ITEM:An,DID_REVERT_ITEM_PROCESSING:An,DID_REQUEST_ITEM_PROCESSING:Ni,DID_ABORT_ITEM_PROCESSING:Gi,DID_COMPLETE_ITEM_PROCESSING:Fi,DID_UPDATE_ITEM_PROCESS_PROGRESS:vi,DID_UPDATE_ITEM_LOAD_PROGRESS:mr,DID_THROW_ITEM_LOAD_ERROR:ke,DID_THROW_ITEM_INVALID:ke,DID_THROW_ITEM_PROCESSING_ERROR:ke,DID_THROW_ITEM_PROCESSING_REVERT_ERROR:ke,DID_THROW_ITEM_REMOVE_ERROR:ke}),didCreateView:e=>{Ge("CREATE_VIEW",{...e,view:e})},create:Ci,mixins:{styles:["translateX","translateY","opacity"],animations:{opacity:{type:"tween",duration:250},translateX:"spring",translateY:"spring"}}}),Pt={AbortItemLoad:{label:"GET_LABEL_BUTTON_ABORT_ITEM_LOAD",action:"ABORT_ITEM_LOAD",className:"filepond--action-abort-item-load",align:"LOAD_INDICATOR_POSITION"},RetryItemLoad:{label:"GET_LABEL_BUTTON_RETRY_ITEM_LOAD",action:"RETRY_ITEM_LOAD",icon:"GET_ICON_RETRY",className:"filepond--action-retry-item-load",align:"BUTTON_PROCESS_ITEM_POSITION"},RemoveItem:{label:"GET_LABEL_BUTTON_REMOVE_ITEM",action:"REQUEST_REMOVE_ITEM",icon:"GET_ICON_REMOVE",className:"filepond--action-remove-item",align:"BUTTON_REMOVE_ITEM_POSITION"},ProcessItem:{label:"GET_LABEL_BUTTON_PROCESS_ITEM",action:"REQUEST_ITEM_PROCESSING",icon:"GET_ICON_PROCESS",className:"filepond--action-process-item",align:"BUTTON_PROCESS_ITEM_POSITION"},AbortItemProcessing:{label:"GET_LABEL_BUTTON_ABORT_ITEM_PROCESSING",action:"ABORT_ITEM_PROCESSING",className:"filepond--action-abort-item-processing",align:"BUTTON_PROCESS_ITEM_POSITION"},RetryItemProcessing:{label:"GET_LABEL_BUTTON_RETRY_ITEM_PROCESSING",action:"RETRY_ITEM_PROCESSING",icon:"GET_ICON_RETRY",className:"filepond--action-retry-item-processing",align:"BUTTON_PROCESS_ITEM_POSITION"},RevertItemProcessing:{label:"GET_LABEL_BUTTON_UNDO_ITEM_PROCESSING",action:"REQUEST_REVERT_ITEM_PROCESSING",icon:"GET_ICON_UNDO",className:"filepond--action-revert-item-processing",align:"BUTTON_PROCESS_ITEM_POSITION"}},Mt=[];Q(Pt,e=>{Mt.push(e)});const Ee=e=>{if(wt(e)==="right")return 0;const t=e.ref.buttonRemoveItem.rect.element;return t.hidden?null:t.width+t.left},Ui=e=>e.ref.buttonAbortItemLoad.rect.element.width,Ke=e=>Math.floor(e.ref.buttonRemoveItem.rect.element.height/4),xi=e=>Math.floor(e.ref.buttonRemoveItem.rect.element.left/2),Vi=e=>e.query("GET_STYLE_LOAD_INDICATOR_POSITION"),qi=e=>e.query("GET_STYLE_PROGRESS_INDICATOR_POSITION"),wt=e=>e.query("GET_STYLE_BUTTON_REMOVE_ITEM_POSITION"),Hi={buttonAbortItemLoad:{opacity:0},buttonRetryItemLoad:{opacity:0},buttonRemoveItem:{opacity:0},buttonProcessItem:{opacity:0},buttonAbortItemProcessing:{opacity:0},buttonRetryItemProcessing:{opacity:0},buttonRevertItemProcessing:{opacity:0},loadProgressIndicator:{opacity:0,align:Vi},processProgressIndicator:{opacity:0,align:qi},processingCompleteIndicator:{opacity:0,scaleX:.75,scaleY:.75},info:{translateX:0,translateY:0,opacity:0},status:{translateX:0,translateY:0,opacity:0}},Ln={buttonRemoveItem:{opacity:1},buttonProcessItem:{opacity:1},info:{translateX:Ee},status:{translateX:Ee}},Ot={buttonAbortItemProcessing:{opacity:1},processProgressIndicator:{opacity:1},status:{opacity:1}},Ue={DID_THROW_ITEM_INVALID:{buttonRemoveItem:{opacity:1},info:{translateX:Ee},status:{translateX:Ee,opacity:1}},DID_START_ITEM_LOAD:{buttonAbortItemLoad:{opacity:1},loadProgressIndicator:{opacity:1},status:{opacity:1}},DID_THROW_ITEM_LOAD_ERROR:{buttonRetryItemLoad:{opacity:1},buttonRemoveItem:{opacity:1},info:{translateX:Ee},status:{opacity:1}},DID_START_ITEM_REMOVE:{processProgressIndicator:{opacity:1,align:wt},info:{translateX:Ee},status:{opacity:0}},DID_THROW_ITEM_REMOVE_ERROR:{processProgressIndicator:{opacity:0,align:wt},buttonRemoveItem:{opacity:1},info:{translateX:Ee},status:{opacity:1,translateX:Ee}},DID_LOAD_ITEM:Ln,DID_LOAD_LOCAL_ITEM:{buttonRemoveItem:{opacity:1},info:{translateX:Ee},status:{translateX:Ee}},DID_START_ITEM_PROCESSING:Ot,DID_REQUEST_ITEM_PROCESSING:Ot,DID_UPDATE_ITEM_PROCESS_PROGRESS:Ot,DID_COMPLETE_ITEM_PROCESSING:{buttonRevertItemProcessing:{opacity:1},info:{opacity:1},status:{opacity:1}},DID_THROW_ITEM_PROCESSING_ERROR:{buttonRemoveItem:{opacity:1},buttonRetryItemProcessing:{opacity:1},status:{opacity:1},info:{translateX:Ee}},DID_THROW_ITEM_PROCESSING_REVERT_ERROR:{buttonRevertItemProcessing:{opacity:1},status:{opacity:1},info:{opacity:1}},DID_ABORT_ITEM_PROCESSING:{buttonRemoveItem:{opacity:1},buttonProcessItem:{opacity:1},info:{translateX:Ee},status:{opacity:1}},DID_REVERT_ITEM_PROCESSING:Ln},Yi=ne({create:({root:e})=>{e.element.innerHTML=e.query("GET_ICON_DONE")},name:"processing-complete-indicator",ignoreRect:!0,mixins:{styles:["scaleX","scaleY","opacity"],animations:{scaleX:"spring",scaleY:"spring",opacity:{type:"tween",duration:250}}}}),zi=({root:e,props:t})=>{const n=Object.keys(Pt).reduce((f,m)=>(f[m]={...Pt[m]},f),{}),{id:r}=t,s=e.query("GET_ALLOW_REVERT"),o=e.query("GET_ALLOW_REMOVE"),i=e.query("GET_ALLOW_PROCESS"),a=e.query("GET_INSTANT_UPLOAD"),l=e.query("IS_ASYNC"),u=e.query("GET_STYLE_BUTTON_REMOVE_ITEM_ALIGN");let c;l?i&&!s?c=f=>!/RevertItemProcessing/.test(f):!i&&s?c=f=>!/ProcessItem|RetryItemProcessing|AbortItemProcessing/.test(f):!i&&!s&&(c=f=>!/Process/.test(f)):c=f=>!/Process/.test(f);const E=c?Mt.filter(c):Mt.concat();if(a&&s&&(n.RevertItemProcessing.label="GET_LABEL_BUTTON_REMOVE_ITEM",n.RevertItemProcessing.icon="GET_ICON_REMOVE"),l&&!s){const f=Ue.DID_COMPLETE_ITEM_PROCESSING;f.info.translateX=xi,f.info.translateY=Ke,f.status.translateY=Ke,f.processingCompleteIndicator={opacity:1,scaleX:1,scaleY:1}}if(l&&!i&&(["DID_START_ITEM_PROCESSING","DID_REQUEST_ITEM_PROCESSING","DID_UPDATE_ITEM_PROCESS_PROGRESS","DID_THROW_ITEM_PROCESSING_ERROR"].forEach(f=>{Ue[f].status.translateY=Ke}),Ue.DID_THROW_ITEM_PROCESSING_ERROR.status.translateX=Ui),u&&s){n.RevertItemProcessing.align="BUTTON_REMOVE_ITEM_POSITION";const f=Ue.DID_COMPLETE_ITEM_PROCESSING;f.info.translateX=Ee,f.status.translateY=Ke,f.processingCompleteIndicator={opacity:1,scaleX:1,scaleY:1}}o||(n.RemoveItem.disabled=!0),Q(n,(f,m)=>{const P=e.createChildView(_r,{label:e.query(m.label),icon:e.query(m.icon),opacity:0});E.includes(f)&&e.appendChildView(P),m.disabled&&(P.element.setAttribute("disabled","disabled"),P.element.setAttribute("hidden","hidden")),P.element.dataset.align=e.query(`GET_STYLE_${m.align}`),P.element.classList.add(m.className),P.on("click",S=>{S.stopPropagation(),!m.disabled&&e.dispatch(m.action,{query:r})}),e.ref[`button${f}`]=P}),e.ref.processingCompleteIndicator=e.appendChildView(e.createChildView(Yi)),e.ref.processingCompleteIndicator.element.dataset.align=e.query("GET_STYLE_BUTTON_PROCESS_ITEM_POSITION"),e.ref.info=e.appendChildView(e.createChildView(wi,{id:r})),e.ref.status=e.appendChildView(e.createChildView(Bi,{id:r}));const g=e.appendChildView(e.createChildView(Sn,{opacity:0,align:e.query("GET_STYLE_LOAD_INDICATOR_POSITION")}));g.element.classList.add("filepond--load-indicator"),e.ref.loadProgressIndicator=g;const I=e.appendChildView(e.createChildView(Sn,{opacity:0,align:e.query("GET_STYLE_PROGRESS_INDICATOR_POSITION")}));I.element.classList.add("filepond--process-indicator"),e.ref.processProgressIndicator=I,e.ref.activeStyles=[]},$i=({root:e,actions:t,props:n})=>{Wi({root:e,actions:t,props:n});let r=t.concat().filter(s=>/^DID_/.test(s.type)).reverse().find(s=>Ue[s.type]);if(r){e.ref.activeStyles=[];const s=Ue[r.type];Q(Hi,(o,i)=>{const a=e.ref[o];Q(i,(l,u)=>{const c=s[o]&&typeof s[o][l]<"u"?s[o][l]:u;e.ref.activeStyles.push({control:a,key:l,value:c})})})}e.ref.activeStyles.forEach(({control:s,key:o,value:i})=>{s[o]=typeof i=="function"?i(e):i})},Wi=ue({DID_SET_LABEL_BUTTON_ABORT_ITEM_PROCESSING:({root:e,action:t})=>{e.ref.buttonAbortItemProcessing.label=t.value},DID_SET_LABEL_BUTTON_ABORT_ITEM_LOAD:({root:e,action:t})=>{e.ref.buttonAbortItemLoad.label=t.value},DID_SET_LABEL_BUTTON_ABORT_ITEM_REMOVAL:({root:e,action:t})=>{e.ref.buttonAbortItemRemoval.label=t.value},DID_REQUEST_ITEM_PROCESSING:({root:e})=>{e.ref.processProgressIndicator.spin=!0,e.ref.processProgressIndicator.progress=0},DID_START_ITEM_LOAD:({root:e})=>{e.ref.loadProgressIndicator.spin=!0,e.ref.loadProgressIndicator.progress=0},DID_START_ITEM_REMOVE:({root:e})=>{e.ref.processProgressIndicator.spin=!0,e.ref.processProgressIndicator.progress=0},DID_UPDATE_ITEM_LOAD_PROGRESS:({root:e,action:t})=>{e.ref.loadProgressIndicator.spin=!1,e.ref.loadProgressIndicator.progress=t.progress},DID_UPDATE_ITEM_PROCESS_PROGRESS:({root:e,action:t})=>{e.ref.processProgressIndicator.spin=!1,e.ref.processProgressIndicator.progress=t.progress}}),ki=ne({create:zi,write:$i,didCreateView:e=>{Ge("CREATE_VIEW",{...e,view:e})},name:"file"}),Xi=({root:e,props:t})=>{e.ref.fileName=Oe("legend"),e.appendChild(e.ref.fileName),e.ref.file=e.appendChildView(e.createChildView(ki,{id:t.id})),e.ref.data=!1},ji=({root:e,props:t})=>{ee(e.ref.fileName,qt(e.query("GET_ITEM_NAME",t.id)))},Qi=ne({create:Xi,ignoreRect:!0,write:ue({DID_LOAD_ITEM:ji}),didCreateView:e=>{Ge("CREATE_VIEW",{...e,view:e})},tag:"fieldset",name:"file-wrapper"}),bn={type:"spring",damping:.6,mass:7},Zi=({root:e,props:t})=>{[{name:"top"},{name:"center",props:{translateY:null,scaleY:null},mixins:{animations:{scaleY:bn},styles:["translateY","scaleY"]}},{name:"bottom",props:{translateY:null},mixins:{animations:{translateY:bn},styles:["translateY"]}}].forEach(n=>{Ki(e,n,t.name)}),e.element.classList.add(`filepond--${t.name}`),e.ref.scalable=null},Ki=(e,t,n)=>{const r=ne({name:`panel-${t.name} filepond--${n}`,mixins:t.mixins,ignoreRectUpdate:!0}),s=e.createChildView(r,t.props);e.ref[t.name]=e.appendChildView(s)},Ji=({root:e,props:t})=>{if((e.ref.scalable===null||t.scalable!==e.ref.scalable)&&(e.ref.scalable=Jn(t.scalable)?t.scalable:!0,e.element.dataset.scalable=e.ref.scalable),!t.height)return;const n=e.ref.top.rect.element,r=e.ref.bottom.rect.element,s=Math.max(n.height+r.height,t.height);e.ref.center.translateY=n.height,e.ref.center.scaleY=(s-n.height-r.height)/100,e.ref.bottom.translateY=s-r.height},gr=ne({name:"panel",read:({root:e,props:t})=>t.heightCurrent=e.ref.bottom.translateY,write:Ji,create:Zi,ignoreRect:!0,mixins:{apis:["height","heightCurrent","scalable"]}}),eo=e=>{const t=e.map(r=>r.id);let n;return{setIndex:r=>{n=r},getIndex:()=>n,getItemIndex:r=>t.indexOf(r.id)}},Pn={type:"spring",stiffness:.75,damping:.45,mass:10},Mn="spring",wn={DID_START_ITEM_LOAD:"busy",DID_UPDATE_ITEM_LOAD_PROGRESS:"loading",DID_THROW_ITEM_INVALID:"load-invalid",DID_THROW_ITEM_LOAD_ERROR:"load-error",DID_LOAD_ITEM:"idle",DID_THROW_ITEM_REMOVE_ERROR:"remove-error",DID_START_ITEM_REMOVE:"busy",DID_START_ITEM_PROCESSING:"busy processing",DID_REQUEST_ITEM_PROCESSING:"busy processing",DID_UPDATE_ITEM_PROCESS_PROGRESS:"processing",DID_COMPLETE_ITEM_PROCESSING:"processing-complete",DID_THROW_ITEM_PROCESSING_ERROR:"processing-error",DID_THROW_ITEM_PROCESSING_REVERT_ERROR:"processing-revert-error",DID_ABORT_ITEM_PROCESSING:"cancelled",DID_REVERT_ITEM_PROCESSING:"idle"},to=({root:e,props:t})=>{if(e.ref.handleClick=r=>e.dispatch("DID_ACTIVATE_ITEM",{id:t.id}),e.element.id=`filepond--item-${t.id}`,e.element.addEventListener("click",e.ref.handleClick),e.ref.container=e.appendChildView(e.createChildView(Qi,{id:t.id})),e.ref.panel=e.appendChildView(e.createChildView(gr,{name:"item-panel"})),e.ref.panel.height=null,t.markedForRemoval=!1,!e.query("GET_ALLOW_REORDER"))return;e.element.dataset.dragState="idle";const n=r=>{if(!r.isPrimary)return;let s=!1;const o={x:r.pageX,y:r.pageY};t.dragOrigin={x:e.translateX,y:e.translateY},t.dragCenter={x:r.offsetX,y:r.offsetY};const i=eo(e.query("GET_ACTIVE_ITEMS"));e.dispatch("DID_GRAB_ITEM",{id:t.id,dragState:i});const a=u=>{if(!u.isPrimary)return;u.stopPropagation(),u.preventDefault(),t.dragOffset={x:u.pageX-o.x,y:u.pageY-o.y},t.dragOffset.x*t.dragOffset.x+t.dragOffset.y*t.dragOffset.y>16&&!s&&(s=!0,e.element.removeEventListener("click",e.ref.handleClick)),e.dispatch("DID_DRAG_ITEM",{id:t.id,dragState:i})},l=u=>{u.isPrimary&&(document.removeEventListener("pointermove",a),document.removeEventListener("pointerup",l),t.dragOffset={x:u.pageX-o.x,y:u.pageY-o.y},e.dispatch("DID_DROP_ITEM",{id:t.id,dragState:i}),s&&setTimeout(()=>e.element.addEventListener("click",e.ref.handleClick),0))};document.addEventListener("pointermove",a),document.addEventListener("pointerup",l)};e.element.addEventListener("pointerdown",n)},no=ue({DID_UPDATE_PANEL_HEIGHT:({root:e,action:t})=>{e.height=t.height}}),ro=ue({DID_GRAB_ITEM:({root:e,props:t})=>{t.dragOrigin={x:e.translateX,y:e.translateY}},DID_DRAG_ITEM:({root:e})=>{e.element.dataset.dragState="drag"},DID_DROP_ITEM:({root:e,props:t})=>{t.dragOffset=null,t.dragOrigin=null,e.element.dataset.dragState="drop"}},({root:e,actions:t,props:n,shouldOptimize:r})=>{e.element.dataset.dragState==="drop"&&e.scaleX<=1&&(e.element.dataset.dragState="idle");let s=t.concat().filter(i=>/^DID_/.test(i.type)).reverse().find(i=>wn[i.type]);s&&s.type!==n.currentState&&(n.currentState=s.type,e.element.dataset.filepondItemState=wn[n.currentState]||"");const o=e.query("GET_ITEM_PANEL_ASPECT_RATIO")||e.query("GET_PANEL_ASPECT_RATIO");o?r||(e.height=e.rect.element.width*o):(no({root:e,actions:t,props:n}),!e.height&&e.ref.container.rect.element.height>0&&(e.height=e.ref.container.rect.element.height)),r&&(e.ref.panel.height=null),e.ref.panel.height=e.height}),so=ne({create:to,write:ro,destroy:({root:e,props:t})=>{e.element.removeEventListener("click",e.ref.handleClick),e.dispatch("RELEASE_ITEM",{query:t.id})},tag:"li",name:"item",mixins:{apis:["id","interactionMethod","markedForRemoval","spawnDate","dragCenter","dragOrigin","dragOffset"],styles:["translateX","translateY","scaleX","scaleY","opacity","height"],animations:{scaleX:Mn,scaleY:Mn,translateX:Pn,translateY:Pn,opacity:{type:"tween",duration:150}}}});var Ht=(e,t)=>Math.max(1,Math.floor((e+1)/t));const Yt=(e,t,n)=>{if(!n)return;const r=e.rect.element.width,s=t.length;let o=null;if(s===0||n.top<t[0].rect.element.top)return-1;const a=t[0].rect.element,l=a.marginLeft+a.marginRight,u=a.width+l,c=Ht(r,u);if(c===1){for(let I=0;I<s;I++){const f=t[I],m=f.rect.outer.top+f.rect.element.height*.5;if(n.top<m)return I}return s}const E=a.marginTop+a.marginBottom,g=a.height+E;for(let I=0;I<s;I++){const f=I%c,m=Math.floor(I/c),P=f*u,S=m*g,b=S-a.marginTop,C=P+u,M=S+g+a.marginBottom;if(n.top<M&&n.top>b){if(n.left<C)return I;I!==s-1?o=I:o=null}}return o!==null?o:s},Je={height:0,width:0,get getHeight(){return this.height},set setHeight(e){(this.height===0||e===0)&&(this.height=e)},get getWidth(){return this.width},set setWidth(e){(this.width===0||e===0)&&(this.width=e)},setDimensions:function(e,t){(this.height===0||e===0)&&(this.height=e),(this.width===0||t===0)&&(this.width=t)}},io=({root:e})=>{te(e.element,"role","list"),e.ref.lastItemSpanwDate=Date.now()},oo=({root:e,action:t})=>{const{id:n,index:r,interactionMethod:s}=t;e.ref.addIndex=r;const o=Date.now();let i=o,a=1;if(s!==Ie.NONE){a=0;const l=e.query("GET_ITEM_INSERT_INTERVAL"),u=o-e.ref.lastItemSpanwDate;i=u<l?o+(l-u):o}e.ref.lastItemSpanwDate=i,e.appendChildView(e.createChildView(so,{spawnDate:i,id:n,opacity:a,interactionMethod:s}),r)},Cn=(e,t,n,r=0,s=1)=>{e.dragOffset?(e.translateX=null,e.translateY=null,e.translateX=e.dragOrigin.x+e.dragOffset.x,e.translateY=e.dragOrigin.y+e.dragOffset.y,e.scaleX=1.025,e.scaleY=1.025):(e.translateX=t,e.translateY=n,Date.now()>e.spawnDate&&(e.opacity===0&&lo(e,t,n,r,s),e.scaleX=1,e.scaleY=1,e.opacity=1))},lo=(e,t,n,r,s)=>{e.interactionMethod===Ie.NONE?(e.translateX=null,e.translateX=t,e.translateY=null,e.translateY=n):e.interactionMethod===Ie.DROP?(e.translateX=null,e.translateX=t-r*20,e.translateY=null,e.translateY=n-s*10,e.scaleX=.8,e.scaleY=.8):e.interactionMethod===Ie.BROWSE?(e.translateY=null,e.translateY=n-30):e.interactionMethod===Ie.API&&(e.translateX=null,e.translateX=t-30,e.translateY=null)},ao=({root:e,action:t})=>{const{id:n}=t,r=e.childViews.find(s=>s.id===n);r&&(r.scaleX=.9,r.scaleY=.9,r.opacity=0,r.markedForRemoval=!0)},St=e=>e.rect.element.height+e.rect.element.marginBottom*.5+e.rect.element.marginTop*.5,co=e=>e.rect.element.width+e.rect.element.marginLeft*.5+e.rect.element.marginRight*.5,uo=({root:e,action:t})=>{const{id:n,dragState:r}=t,s=e.query("GET_ITEM",{id:n}),o=e.childViews.find(P=>P.id===n),i=e.childViews.length,a=r.getItemIndex(s);if(!o)return;const l={x:o.dragOrigin.x+o.dragOffset.x+o.dragCenter.x,y:o.dragOrigin.y+o.dragOffset.y+o.dragCenter.y},u=St(o),c=co(o);let E=Math.floor(e.rect.outer.width/c);E>i&&(E=i);const g=Math.floor(i/E+1);Je.setHeight=u*g,Je.setWidth=c*E;var I={y:Math.floor(l.y/u),x:Math.floor(l.x/c),getGridIndex:function(){return l.y>Je.getHeight||l.y<0||l.x>Je.getWidth||l.x<0?a:this.y*E+this.x},getColIndex:function(){const S=e.query("GET_ACTIVE_ITEMS"),b=e.childViews.filter(N=>N.rect.element.height),C=S.map(N=>b.find(U=>U.id===N.id)),M=C.findIndex(N=>N===o),w=St(o),F=C.length;let W=F,A=0,B=0,Y=0;for(let N=0;N<F;N++)if(A=St(C[N]),Y=B,B=Y+A,l.y<B){if(M>N){if(l.y<Y+w){W=N;break}continue}W=N;break}return W}};const f=E>1?I.getGridIndex():I.getColIndex();e.dispatch("MOVE_ITEM",{query:o,index:f});const m=r.getIndex();if(m===void 0||m!==f){if(r.setIndex(f),m===void 0)return;e.dispatch("DID_REORDER_ITEMS",{items:e.query("GET_ACTIVE_ITEMS"),origin:a,target:f})}},fo=ue({DID_ADD_ITEM:oo,DID_REMOVE_ITEM:ao,DID_DRAG_ITEM:uo}),Eo=({root:e,props:t,actions:n,shouldOptimize:r})=>{fo({root:e,props:t,actions:n});const{dragCoordinates:s}=t,o=e.rect.element.width,i=e.childViews.filter(C=>C.rect.element.height),a=e.query("GET_ACTIVE_ITEMS").map(C=>i.find(M=>M.id===C.id)).filter(C=>C),l=s?Yt(e,a,s):null,u=e.ref.addIndex||null;e.ref.addIndex=null;let c=0,E=0,g=0;if(a.length===0)return;const I=a[0].rect.element,f=I.marginTop+I.marginBottom,m=I.marginLeft+I.marginRight,P=I.width+m,S=I.height+f,b=Ht(o,P);if(b===1){let C=0,M=0;a.forEach((w,F)=>{if(l){let B=F-l;B===-2?M=-f*.25:B===-1?M=-f*.75:B===0?M=f*.75:B===1?M=f*.25:M=0}r&&(w.translateX=null,w.translateY=null),w.markedForRemoval||Cn(w,0,C+M);let A=(w.rect.element.height+f)*(w.markedForRemoval?w.opacity:1);C+=A})}else{let C=0,M=0;a.forEach((w,F)=>{F===l&&(c=1),F===u&&(g+=1),w.markedForRemoval&&w.opacity<.5&&(E-=1);const W=F+g+c+E,A=W%b,B=Math.floor(W/b),Y=A*P,N=B*S,U=Math.sign(Y-C),X=Math.sign(N-M);C=Y,M=N,!w.markedForRemoval&&(r&&(w.translateX=null,w.translateY=null),Cn(w,Y,N,U,X))})}},po=(e,t)=>t.filter(n=>n.data&&n.data.id?e.id===n.data.id:!0),_o=ne({create:io,write:Eo,tag:"ul",name:"list",didWriteView:({root:e})=>{e.childViews.filter(t=>t.markedForRemoval&&t.opacity===0&&t.resting).forEach(t=>{t._destroy(),e.removeChildView(t)})},filterFrameActionsForChild:po,mixins:{apis:["dragCoordinates"]}}),Io=({root:e,props:t})=>{e.ref.list=e.appendChildView(e.createChildView(_o)),t.dragCoordinates=null,t.overflowing=!1},To=({root:e,props:t,action:n})=>{e.query("GET_ITEM_INSERT_LOCATION_FREEDOM")&&(t.dragCoordinates={left:n.position.scopeLeft-e.ref.list.rect.element.left,top:n.position.scopeTop-(e.rect.outer.top+e.rect.element.marginTop+e.rect.element.scrollTop)})},mo=({props:e})=>{e.dragCoordinates=null},go=ue({DID_DRAG:To,DID_END_DRAG:mo}),ho=({root:e,props:t,actions:n})=>{if(go({root:e,props:t,actions:n}),e.ref.list.dragCoordinates=t.dragCoordinates,t.overflowing&&!t.overflow&&(t.overflowing=!1,e.element.dataset.state="",e.height=null),t.overflow){const r=Math.round(t.overflow);r!==e.height&&(t.overflowing=!0,e.element.dataset.state="overflow",e.height=r)}},Ro=ne({create:Io,write:ho,name:"list-scroller",mixins:{apis:["overflow","dragCoordinates"],styles:["height","translateY"],animations:{translateY:"spring"}}}),he=(e,t,n,r="")=>{n?te(e,t,r):e.removeAttribute(t)},Oo=e=>{if(!(!e||e.value==="")){try{e.value=""}catch{}if(e.value){const t=Oe("form"),n=e.parentNode,r=e.nextSibling;t.appendChild(e),t.reset(),r?n.insertBefore(e,r):n.appendChild(e)}}},So=({root:e,props:t})=>{e.element.id=`filepond--browser-${t.id}`,te(e.element,"name",e.query("GET_NAME")),te(e.element,"aria-controls",`filepond--assistant-${t.id}`),te(e.element,"aria-labelledby",`filepond--drop-label-${t.id}`),hr({root:e,action:{value:e.query("GET_ACCEPTED_FILE_TYPES")}}),Rr({root:e,action:{value:e.query("GET_ALLOW_MULTIPLE")}}),Or({root:e,action:{value:e.query("GET_ALLOW_DIRECTORIES_ONLY")}}),Ct({root:e}),Sr({root:e,action:{value:e.query("GET_REQUIRED")}}),Dr({root:e,action:{value:e.query("GET_CAPTURE_METHOD")}}),e.ref.handleChange=n=>{if(!e.element.value)return;const r=Array.from(e.element.files).map(s=>(s._relativePath=s.webkitRelativePath,s));setTimeout(()=>{t.onload(r),Oo(e.element)},250)},e.element.addEventListener("change",e.ref.handleChange)},hr=({root:e,action:t})=>{e.query("GET_ALLOW_SYNC_ACCEPT_ATTRIBUTE")&&he(e.element,"accept",!!t.value,t.value?t.value.join(","):"")},Rr=({root:e,action:t})=>{he(e.element,"multiple",t.value)},Or=({root:e,action:t})=>{he(e.element,"webkitdirectory",t.value)},Ct=({root:e})=>{const t=e.query("GET_DISABLED"),n=e.query("GET_ALLOW_BROWSE"),r=t||!n;he(e.element,"disabled",r)},Sr=({root:e,action:t})=>{t.value?e.query("GET_TOTAL_ITEMS")===0&&he(e.element,"required",!0):he(e.element,"required",!1)},Dr=({root:e,action:t})=>{he(e.element,"capture",!!t.value,t.value===!0?"":t.value)},vn=({root:e})=>{const{element:t}=e;e.query("GET_TOTAL_ITEMS")>0?(he(t,"required",!1),he(t,"name",!1)):(he(t,"name",!0,e.query("GET_NAME")),e.query("GET_CHECK_VALIDITY")&&t.setCustomValidity(""),e.query("GET_REQUIRED")&&he(t,"required",!0))},Do=({root:e})=>{e.query("GET_CHECK_VALIDITY")&&e.element.setCustomValidity(e.query("GET_LABEL_INVALID_FIELD"))},yo=ne({tag:"input",name:"browser",ignoreRect:!0,ignoreRectUpdate:!0,attributes:{type:"file"},create:So,destroy:({root:e})=>{e.element.removeEventListener("change",e.ref.handleChange)},write:ue({DID_LOAD_ITEM:vn,DID_REMOVE_ITEM:vn,DID_THROW_ITEM_INVALID:Do,DID_SET_DISABLED:Ct,DID_SET_ALLOW_BROWSE:Ct,DID_SET_ALLOW_DIRECTORIES_ONLY:Or,DID_SET_ALLOW_MULTIPLE:Rr,DID_SET_ACCEPTED_FILE_TYPES:hr,DID_SET_CAPTURE_METHOD:Dr,DID_SET_REQUIRED:Sr})}),Nn={ENTER:13,SPACE:32},Ao=({root:e,props:t})=>{const n=Oe("label");te(n,"for",`filepond--browser-${t.id}`),te(n,"id",`filepond--drop-label-${t.id}`),te(n,"aria-hidden","true"),e.ref.handleKeyDown=r=>{(r.keyCode===Nn.ENTER||r.keyCode===Nn.SPACE)&&(r.preventDefault(),e.ref.label.click())},e.ref.handleClick=r=>{r.target===n||n.contains(r.target)||e.ref.label.click()},n.addEventListener("keydown",e.ref.handleKeyDown),e.element.addEventListener("click",e.ref.handleClick),yr(n,t.caption),e.appendChild(n),e.ref.label=n},yr=(e,t)=>{e.innerHTML=t;const n=e.querySelector(".filepond--label-action");return n&&te(n,"tabindex","0"),t},Lo=ne({name:"drop-label",ignoreRect:!0,create:Ao,destroy:({root:e})=>{e.ref.label.addEventListener("keydown",e.ref.handleKeyDown),e.element.removeEventListener("click",e.ref.handleClick)},write:ue({DID_SET_LABEL_IDLE:({root:e,action:t})=>{yr(e.ref.label,t.value)}}),mixins:{styles:["opacity","translateX","translateY"],animations:{opacity:{type:"tween",duration:150},translateX:"spring",translateY:"spring"}}}),bo=ne({name:"drip-blob",ignoreRect:!0,mixins:{styles:["translateX","translateY","scaleX","scaleY","opacity"],animations:{scaleX:"spring",scaleY:"spring",translateX:"spring",translateY:"spring",opacity:{type:"tween",duration:250}}}}),Po=({root:e})=>{const t=e.rect.element.width*.5,n=e.rect.element.height*.5;e.ref.blob=e.appendChildView(e.createChildView(bo,{opacity:0,scaleX:2.5,scaleY:2.5,translateX:t,translateY:n}))},Mo=({root:e,action:t})=>{if(!e.ref.blob){Po({root:e});return}e.ref.blob.translateX=t.position.scopeLeft,e.ref.blob.translateY=t.position.scopeTop,e.ref.blob.scaleX=1,e.ref.blob.scaleY=1,e.ref.blob.opacity=1},wo=({root:e})=>{e.ref.blob&&(e.ref.blob.opacity=0)},Co=({root:e})=>{e.ref.blob&&(e.ref.blob.scaleX=2.5,e.ref.blob.scaleY=2.5,e.ref.blob.opacity=0)},vo=({root:e,props:t,actions:n})=>{No({root:e,props:t,actions:n});const{blob:r}=e.ref;n.length===0&&r&&r.opacity===0&&(e.removeChildView(r),e.ref.blob=null)},No=ue({DID_DRAG:Mo,DID_DROP:Co,DID_END_DRAG:wo}),Go=ne({ignoreRect:!0,ignoreRectUpdate:!0,name:"drip",write:vo}),Ar=(e,t)=>{try{const n=new DataTransfer;t.forEach(r=>{r instanceof File?n.items.add(r):n.items.add(new File([r],r.name,{type:r.type}))}),e.files=n.files}catch{return!1}return!0},Fo=({root:e})=>e.ref.fields={},Et=(e,t)=>e.ref.fields[t],zt=e=>{e.query("GET_ACTIVE_ITEMS").forEach(t=>{e.ref.fields[t.id]&&e.element.appendChild(e.ref.fields[t.id])})},Gn=({root:e})=>zt(e),Bo=({root:e,action:t})=>{const s=!(e.query("GET_ITEM",t.id).origin===se.LOCAL)&&e.query("SHOULD_UPDATE_FILE_INPUT"),o=Oe("input");o.type=s?"file":"hidden",o.name=e.query("GET_NAME"),o.disabled=e.query("GET_DISABLED"),e.ref.fields[t.id]=o,zt(e)},Uo=({root:e,action:t})=>{const n=Et(e,t.id);if(!n||(t.serverFileReference!==null&&(n.value=t.serverFileReference),!e.query("SHOULD_UPDATE_FILE_INPUT")))return;const r=e.query("GET_ITEM",t.id);Ar(n,[r.file])},xo=({root:e,action:t})=>{e.query("SHOULD_UPDATE_FILE_INPUT")&&setTimeout(()=>{const n=Et(e,t.id);n&&Ar(n,[t.file])},0)},Vo=({root:e})=>{e.element.disabled=e.query("GET_DISABLED")},qo=({root:e,action:t})=>{const n=Et(e,t.id);n&&(n.parentNode&&n.parentNode.removeChild(n),delete e.ref.fields[t.id])},Ho=({root:e,action:t})=>{const n=Et(e,t.id);n&&(t.value===null?n.removeAttribute("value"):n.type!="file"&&(n.value=t.value),zt(e))},Yo=ue({DID_SET_DISABLED:Vo,DID_ADD_ITEM:Bo,DID_LOAD_ITEM:Uo,DID_REMOVE_ITEM:qo,DID_DEFINE_VALUE:Ho,DID_PREPARE_OUTPUT:xo,DID_REORDER_ITEMS:Gn,DID_SORT_ITEMS:Gn}),zo=ne({tag:"fieldset",name:"data",create:Fo,write:Yo,ignoreRect:!0}),$o=e=>"getRootNode"in e?e.getRootNode():document,Wo=["jpg","jpeg","png","gif","bmp","webp","svg","tiff"],ko=["css","csv","html","txt"],Xo={zip:"zip|compressed",epub:"application/epub+zip"},Lr=(e="")=>(e=e.toLowerCase(),Wo.includes(e)?"image/"+(e==="jpg"?"jpeg":e==="svg"?"svg+xml":e):ko.includes(e)?"text/"+e:Xo[e]||""),$t=e=>new Promise((t,n)=>{const r=nl(e);if(r.length&&!jo(e))return t(r);Qo(e).then(t)}),jo=e=>e.files?e.files.length>0:!1,Qo=e=>new Promise((t,n)=>{const r=(e.items?Array.from(e.items):[]).filter(s=>Zo(s)).map(s=>Ko(s));if(!r.length){t(e.files?Array.from(e.files):[]);return}Promise.all(r).then(s=>{const o=[];s.forEach(i=>{o.push.apply(o,i)}),t(o.filter(i=>i).map(i=>(i._relativePath||(i._relativePath=i.webkitRelativePath),i)))}).catch(console.error)}),Zo=e=>{if(br(e)){const t=Wt(e);if(t)return t.isFile||t.isDirectory}return e.kind==="file"},Ko=e=>new Promise((t,n)=>{if(tl(e)){Jo(Wt(e)).then(t).catch(n);return}t([e.getAsFile()])}),Jo=e=>new Promise((t,n)=>{const r=[];let s=0,o=0;const i=()=>{o===0&&s===0&&t(r)},a=l=>{s++;const u=l.createReader(),c=()=>{u.readEntries(E=>{if(E.length===0){s--,i();return}E.forEach(g=>{g.isDirectory?a(g):(o++,g.file(I=>{const f=el(I);g.fullPath&&(f._relativePath=g.fullPath),r.push(f),o--,i()}))}),c()},n)};c()};a(e)}),el=e=>{if(e.type.length)return e;const t=e.lastModifiedDate,n=e.name,r=Lr(ft(e.name));return r.length&&(e=e.slice(0,e.size,r),e.name=n,e.lastModifiedDate=t),e},tl=e=>br(e)&&(Wt(e)||{}).isDirectory,br=e=>"webkitGetAsEntry"in e,Wt=e=>e.webkitGetAsEntry(),nl=e=>{let t=[];try{if(t=sl(e),t.length)return t;t=rl(e)}catch{}return t},rl=e=>{let t=e.getData("url");return typeof t=="string"&&t.length?[t]:[]},sl=e=>{let t=e.getData("text/html");if(typeof t=="string"&&t.length){const n=t.match(/src\s*=\s*"(.+?)"/);if(n)return[n[1]]}return[]},ot=[],Ne=e=>({pageLeft:e.pageX,pageTop:e.pageY,scopeLeft:e.offsetX||e.layerX,scopeTop:e.offsetY||e.layerY}),il=(e,t,n)=>{const r=ol(t),s={element:e,filterElement:n,state:null,ondrop:()=>{},onenter:()=>{},ondrag:()=>{},onexit:()=>{},onload:()=>{},allowdrop:()=>{}};return s.destroy=r.addListener(s),s},ol=e=>{const t=ot.find(r=>r.element===e);if(t)return t;const n=ll(e);return ot.push(n),n},ll=e=>{const t=[],n={dragenter:cl,dragover:ul,dragleave:fl,drop:dl},r={};Q(n,(o,i)=>{r[o]=i(e,t),e.addEventListener(o,r[o],!1)});const s={element:e,addListener:o=>(t.push(o),()=>{t.splice(t.indexOf(o),1),t.length===0&&(ot.splice(ot.indexOf(s),1),Q(n,i=>{e.removeEventListener(i,r[i],!1)}))})};return s},al=(e,t)=>("elementFromPoint"in e||(e=document),e.elementFromPoint(t.x,t.y)),kt=(e,t)=>{const n=$o(t),r=al(n,{x:e.pageX-window.pageXOffset,y:e.pageY-window.pageYOffset});return r===t||t.contains(r)};let Pr=null;const et=(e,t)=>{try{e.dropEffect=t}catch{}},cl=(e,t)=>n=>{n.preventDefault(),Pr=n.target,t.forEach(r=>{const{element:s,onenter:o}=r;kt(n,s)&&(r.state="enter",o(Ne(n)))})},ul=(e,t)=>n=>{n.preventDefault();const r=n.dataTransfer;$t(r).then(s=>{let o=!1;t.some(i=>{const{filterElement:a,element:l,onenter:u,onexit:c,ondrag:E,allowdrop:g}=i;et(r,"copy");const I=g(s);if(!I){et(r,"none");return}if(kt(n,l)){if(o=!0,i.state===null){i.state="enter",u(Ne(n));return}if(i.state="over",a&&!I){et(r,"none");return}E(Ne(n))}else a&&!o&&et(r,"none"),i.state&&(i.state=null,c(Ne(n)))})})},dl=(e,t)=>n=>{n.preventDefault();const r=n.dataTransfer;$t(r).then(s=>{t.forEach(o=>{const{filterElement:i,element:a,ondrop:l,onexit:u,allowdrop:c}=o;if(o.state=null,!(i&&!kt(n,a))){if(!c(s))return u(Ne(n));l(Ne(n),s)}})})},fl=(e,t)=>n=>{Pr===n.target&&t.forEach(r=>{const{onexit:s}=r;r.state=null,s(Ne(n))})},El=(e,t,n)=>{e.classList.add("filepond--hopper");const{catchesDropsOnPage:r,requiresDropOnElement:s,filterItems:o=c=>c}=n,i=il(e,r?document.documentElement:e,s);let a="",l="";i.allowdrop=c=>t(o(c)),i.ondrop=(c,E)=>{const g=o(E);if(!t(g)){u.ondragend(c);return}l="drag-drop",u.onload(g,c)},i.ondrag=c=>{u.ondrag(c)},i.onenter=c=>{l="drag-over",u.ondragstart(c)},i.onexit=c=>{l="drag-exit",u.ondragend(c)};const u={updateHopperState:()=>{a!==l&&(e.dataset.hopperState=l,a=l)},onload:()=>{},ondragstart:()=>{},ondrag:()=>{},ondragend:()=>{},destroy:()=>{i.destroy()}};return u};let vt=!1;const xe=[],Mr=e=>{const t=document.activeElement;if(t&&/textarea|input/i.test(t.nodeName)){let n=!1,r=t;for(;r!==document.body;){if(r.classList.contains("filepond--root")){n=!0;break}r=r.parentNode}if(!n)return}$t(e.clipboardData).then(n=>{n.length&&xe.forEach(r=>r(n))})},pl=e=>{xe.includes(e)||(xe.push(e),!vt&&(vt=!0,document.addEventListener("paste",Mr)))},_l=e=>{Ut(xe,xe.indexOf(e)),xe.length===0&&(document.removeEventListener("paste",Mr),vt=!1)},Il=()=>{const e=n=>{t.onload(n)},t={destroy:()=>{_l(e)},onload:()=>{}};return pl(e),t},Tl=({root:e,props:t})=>{e.element.id=`filepond--assistant-${t.id}`,te(e.element,"role","status"),te(e.element,"aria-live","polite"),te(e.element,"aria-relevant","additions")};let Fn=null,Bn=null;const Dt=[],pt=(e,t)=>{e.element.textContent=t},ml=e=>{e.element.textContent=""},wr=(e,t,n)=>{const r=e.query("GET_TOTAL_ITEMS");pt(e,`${n} ${t}, ${r} ${r===1?e.query("GET_LABEL_FILE_COUNT_SINGULAR"):e.query("GET_LABEL_FILE_COUNT_PLURAL")}`),clearTimeout(Bn),Bn=setTimeout(()=>{ml(e)},1500)},Cr=e=>e.element.parentNode.contains(document.activeElement),gl=({root:e,action:t})=>{if(!Cr(e))return;e.element.textContent="";const n=e.query("GET_ITEM",t.id);Dt.push(n.filename),clearTimeout(Fn),Fn=setTimeout(()=>{wr(e,Dt.join(", "),e.query("GET_LABEL_FILE_ADDED")),Dt.length=0},750)},hl=({root:e,action:t})=>{if(!Cr(e))return;const n=t.item;wr(e,n.filename,e.query("GET_LABEL_FILE_REMOVED"))},Rl=({root:e,action:t})=>{const r=e.query("GET_ITEM",t.id).filename,s=e.query("GET_LABEL_FILE_PROCESSING_COMPLETE");pt(e,`${r} ${s}`)},Un=({root:e,action:t})=>{const r=e.query("GET_ITEM",t.id).filename,s=e.query("GET_LABEL_FILE_PROCESSING_ABORTED");pt(e,`${r} ${s}`)},tt=({root:e,action:t})=>{const r=e.query("GET_ITEM",t.id).filename;pt(e,`${t.status.main} ${r} ${t.status.sub}`)},Ol=ne({create:Tl,ignoreRect:!0,ignoreRectUpdate:!0,write:ue({DID_LOAD_ITEM:gl,DID_REMOVE_ITEM:hl,DID_COMPLETE_ITEM_PROCESSING:Rl,DID_ABORT_ITEM_PROCESSING:Un,DID_REVERT_ITEM_PROCESSING:Un,DID_THROW_ITEM_REMOVE_ERROR:tt,DID_THROW_ITEM_LOAD_ERROR:tt,DID_THROW_ITEM_INVALID:tt,DID_THROW_ITEM_PROCESSING_ERROR:tt}),tag:"span",name:"assistant"}),vr=(e,t="-")=>e.replace(new RegExp(`${t}.`,"g"),n=>n.charAt(1).toUpperCase()),Nr=(e,t=16,n=!0)=>{let r=Date.now(),s=null;return(...o)=>{clearTimeout(s);const i=Date.now()-r,a=()=>{r=Date.now(),e(...o)};i<t?n||(s=setTimeout(a,t-i)):a()}},Sl=1e6,lt=e=>e.preventDefault(),Dl=({root:e,props:t})=>{const n=e.query("GET_ID");n&&(e.element.id=n);const r=e.query("GET_CLASS_NAME");r&&r.split(" ").filter(l=>l.length).forEach(l=>{e.element.classList.add(l)}),e.ref.label=e.appendChildView(e.createChildView(Lo,{...t,translateY:null,caption:e.query("GET_LABEL_IDLE")})),e.ref.list=e.appendChildView(e.createChildView(Ro,{translateY:null})),e.ref.panel=e.appendChildView(e.createChildView(gr,{name:"panel-root"})),e.ref.assistant=e.appendChildView(e.createChildView(Ol,{...t})),e.ref.data=e.appendChildView(e.createChildView(zo,{...t})),e.ref.measure=Oe("div"),e.ref.measure.style.height="100%",e.element.appendChild(e.ref.measure),e.ref.bounds=null,e.query("GET_STYLES").filter(l=>!Re(l.value)).map(({name:l,value:u})=>{e.element.dataset[l]=u}),e.ref.widthPrevious=null,e.ref.widthUpdated=Nr(()=>{e.ref.updateHistory=[],e.dispatch("DID_RESIZE_ROOT")},250),e.ref.previousAspectRatio=null,e.ref.updateHistory=[];const s=window.matchMedia("(pointer: fine) and (hover: hover)").matches,o="PointerEvent"in window;e.query("GET_ALLOW_REORDER")&&o&&!s&&(e.element.addEventListener("touchmove",lt,{passive:!1}),e.element.addEventListener("gesturestart",lt));const i=e.query("GET_CREDITS");if(i.length===2){const l=document.createElement("a");l.className="filepond--credits",l.setAttribute("aria-hidden","true"),l.href=i[0],l.tabindex=-1,l.target="_blank",l.rel="noopener noreferrer",l.textContent=i[1],e.element.appendChild(l),e.ref.credits=l}},yl=({root:e,props:t,actions:n})=>{if(Ml({root:e,props:t,actions:n}),n.filter(F=>/^DID_SET_STYLE_/.test(F.type)).filter(F=>!Re(F.data.value)).map(({type:F,data:W})=>{const A=vr(F.substring(8).toLowerCase(),"_");e.element.dataset[A]=W.value,e.invalidateLayout()}),e.rect.element.hidden)return;e.rect.element.width!==e.ref.widthPrevious&&(e.ref.widthPrevious=e.rect.element.width,e.ref.widthUpdated());let r=e.ref.bounds;r||(r=e.ref.bounds=bl(e),e.element.removeChild(e.ref.measure),e.ref.measure=null);const{hopper:s,label:o,list:i,panel:a}=e.ref;s&&s.updateHopperState();const l=e.query("GET_PANEL_ASPECT_RATIO"),u=e.query("GET_ALLOW_MULTIPLE"),c=e.query("GET_TOTAL_ITEMS"),E=u?e.query("GET_MAX_FILES")||Sl:1,g=c===E,I=n.find(F=>F.type==="DID_ADD_ITEM");if(g&&I){const F=I.data.interactionMethod;o.opacity=0,u?o.translateY=-40:F===Ie.API?o.translateX=40:F===Ie.BROWSE?o.translateY=40:o.translateY=30}else g||(o.opacity=1,o.translateX=0,o.translateY=0);const f=Al(e),m=Ll(e),P=o.rect.element.height,S=!u||g?0:P,b=g?i.rect.element.marginTop:0,C=c===0?0:i.rect.element.marginBottom,M=S+b+m.visual+C,w=S+b+m.bounds+C;if(i.translateY=Math.max(0,S-i.rect.element.marginTop)-f.top,l){const F=e.rect.element.width,W=F*l;l!==e.ref.previousAspectRatio&&(e.ref.previousAspectRatio=l,e.ref.updateHistory=[]);const A=e.ref.updateHistory;A.push(F);const B=2;if(A.length>B*2){const N=A.length,U=N-10;let X=0;for(let y=N;y>=U;y--)if(A[y]===A[y-2]&&X++,X>=B)return}a.scalable=!1,a.height=W;const Y=W-S-(C-f.bottom)-(g?b:0);m.visual>Y?i.overflow=Y:i.overflow=null,e.height=W}else if(r.fixedHeight){a.scalable=!1;const F=r.fixedHeight-S-(C-f.bottom)-(g?b:0);m.visual>F?i.overflow=F:i.overflow=null}else if(r.cappedHeight){const F=M>=r.cappedHeight,W=Math.min(r.cappedHeight,M);a.scalable=!0,a.height=F?W:W-f.top-f.bottom;const A=W-S-(C-f.bottom)-(g?b:0);M>r.cappedHeight&&m.visual>A?i.overflow=A:i.overflow=null,e.height=Math.min(r.cappedHeight,w-f.top-f.bottom)}else{const F=c>0?f.top+f.bottom:0;a.scalable=!0,a.height=Math.max(P,M-F),e.height=Math.max(P,w-F)}e.ref.credits&&a.heightCurrent&&(e.ref.credits.style.transform=`translateY(${a.heightCurrent}px)`)},Al=e=>{const t=e.ref.list.childViews[0].childViews[0];return t?{top:t.rect.element.marginTop,bottom:t.rect.element.marginBottom}:{top:0,bottom:0}},Ll=e=>{let t=0,n=0;const r=e.ref.list,s=r.childViews[0],o=s.childViews.filter(b=>b.rect.element.height),i=e.query("GET_ACTIVE_ITEMS").map(b=>o.find(C=>C.id===b.id)).filter(b=>b);if(i.length===0)return{visual:t,bounds:n};const a=s.rect.element.width,l=Yt(s,i,r.dragCoordinates),u=i[0].rect.element,c=u.marginTop+u.marginBottom,E=u.marginLeft+u.marginRight,g=u.width+E,I=u.height+c,f=typeof l<"u"&&l>=0?1:0,m=i.find(b=>b.markedForRemoval&&b.opacity<.45)?-1:0,P=i.length+f+m,S=Ht(a,g);return S===1?i.forEach(b=>{const C=b.rect.element.height+c;n+=C,t+=C*b.opacity}):(n=Math.ceil(P/S)*I,t=n),{visual:t,bounds:n}},bl=e=>{const t=e.ref.measureHeight||null;return{cappedHeight:parseInt(e.style.maxHeight,10)||null,fixedHeight:t===0?null:t}},Xt=(e,t)=>{const n=e.query("GET_ALLOW_REPLACE"),r=e.query("GET_ALLOW_MULTIPLE"),s=e.query("GET_TOTAL_ITEMS");let o=e.query("GET_MAX_FILES");const i=t.length;return!r&&i>1?(e.dispatch("DID_THROW_MAX_FILES",{source:t,error:J("warning",0,"Max files")}),!0):(o=r?o:1,!r&&n?!1:He(o)&&s+i>o?(e.dispatch("DID_THROW_MAX_FILES",{source:t,error:J("warning",0,"Max files")}),!0):!1)},Pl=(e,t,n)=>{const r=e.childViews[0];return Yt(r,t,{left:n.scopeLeft-r.rect.element.left,top:n.scopeTop-(e.rect.outer.top+e.rect.element.marginTop+e.rect.element.scrollTop)})},xn=e=>{const t=e.query("GET_ALLOW_DROP"),n=e.query("GET_DISABLED"),r=t&&!n;if(r&&!e.ref.hopper){const s=El(e.element,o=>{const i=e.query("GET_BEFORE_DROP_FILE")||(()=>!0);return e.query("GET_DROP_VALIDATION")?o.every(l=>Ge("ALLOW_HOPPER_ITEM",l,{query:e.query}).every(u=>u===!0)&&i(l)):!0},{filterItems:o=>{const i=e.query("GET_IGNORED_FILES");return o.filter(a=>qe(a)?!i.includes(a.name.toLowerCase()):!0)},catchesDropsOnPage:e.query("GET_DROP_ON_PAGE"),requiresDropOnElement:e.query("GET_DROP_ON_ELEMENT")});s.onload=(o,i)=>{const l=e.ref.list.childViews[0].childViews.filter(c=>c.rect.element.height),u=e.query("GET_ACTIVE_ITEMS").map(c=>l.find(E=>E.id===c.id)).filter(c=>c);me("ADD_ITEMS",o,{dispatch:e.dispatch}).then(c=>{if(Xt(e,c))return!1;e.dispatch("ADD_ITEMS",{items:c,index:Pl(e.ref.list,u,i),interactionMethod:Ie.DROP})}),e.dispatch("DID_DROP",{position:i}),e.dispatch("DID_END_DRAG",{position:i})},s.ondragstart=o=>{e.dispatch("DID_START_DRAG",{position:o})},s.ondrag=Nr(o=>{e.dispatch("DID_DRAG",{position:o})}),s.ondragend=o=>{e.dispatch("DID_END_DRAG",{position:o})},e.ref.hopper=s,e.ref.drip=e.appendChildView(e.createChildView(Go))}else!r&&e.ref.hopper&&(e.ref.hopper.destroy(),e.ref.hopper=null,e.removeChildView(e.ref.drip))},Vn=(e,t)=>{const n=e.query("GET_ALLOW_BROWSE"),r=e.query("GET_DISABLED"),s=n&&!r;s&&!e.ref.browser?e.ref.browser=e.appendChildView(e.createChildView(yo,{...t,onload:o=>{me("ADD_ITEMS",o,{dispatch:e.dispatch}).then(i=>{if(Xt(e,i))return!1;e.dispatch("ADD_ITEMS",{items:i,index:-1,interactionMethod:Ie.BROWSE})})}}),0):!s&&e.ref.browser&&(e.removeChildView(e.ref.browser),e.ref.browser=null)},qn=e=>{const t=e.query("GET_ALLOW_PASTE"),n=e.query("GET_DISABLED"),r=t&&!n;r&&!e.ref.paster?(e.ref.paster=Il(),e.ref.paster.onload=s=>{me("ADD_ITEMS",s,{dispatch:e.dispatch}).then(o=>{if(Xt(e,o))return!1;e.dispatch("ADD_ITEMS",{items:o,index:-1,interactionMethod:Ie.PASTE})})}):!r&&e.ref.paster&&(e.ref.paster.destroy(),e.ref.paster=null)},Ml=ue({DID_SET_ALLOW_BROWSE:({root:e,props:t})=>{Vn(e,t)},DID_SET_ALLOW_DROP:({root:e})=>{xn(e)},DID_SET_ALLOW_PASTE:({root:e})=>{qn(e)},DID_SET_DISABLED:({root:e,props:t})=>{xn(e),qn(e),Vn(e,t),e.query("GET_DISABLED")?e.element.dataset.disabled="disabled":e.element.removeAttribute("data-disabled")}}),wl=ne({name:"root",read:({root:e})=>{e.ref.measure&&(e.ref.measureHeight=e.ref.measure.offsetHeight)},create:Dl,write:yl,destroy:({root:e})=>{e.ref.paster&&e.ref.paster.destroy(),e.ref.hopper&&e.ref.hopper.destroy(),e.element.removeEventListener("touchmove",lt),e.element.removeEventListener("gesturestart",lt)},mixins:{styles:["height"]}}),Cl=(e={})=>{let t=null;const n=it(),r=Xr(vs(n),[Zs,Fs(n)],[Oi,Gs(n)]);r.dispatch("SET_OPTIONS",{options:e});const s=()=>{document.hidden||r.dispatch("KICK")};document.addEventListener("visibilitychange",s);let o=null,i=!1,a=!1,l=null,u=null;const c=()=>{i||(i=!0),clearTimeout(o),o=setTimeout(()=>{i=!1,l=null,u=null,a&&(a=!1,r.dispatch("DID_STOP_RESIZE"))},500)};window.addEventListener("resize",c);const E=wl(r,{id:Bt()});let g=!1,I=!1;const f={_read:()=>{i&&(u=window.innerWidth,l||(l=u),!a&&u!==l&&(r.dispatch("DID_START_RESIZE"),a=!0)),I&&g&&(g=E.element.offsetParent===null),!g&&(E._read(),I=E.rect.element.hidden)},_write:p=>{const D=r.processActionQueue().filter(G=>!/^SET_/.test(G.type));g&&!D.length||(b(D),g=E._write(p,D,a),xs(r.query("GET_ITEMS")),g&&r.processDispatchQueue())}},m=p=>D=>{const G={type:p};if(!D)return G;if(D.hasOwnProperty("error")&&(G.error=D.error?{...D.error}:null),D.status&&(G.status={...D.status}),D.file&&(G.output=D.file),D.source)G.file=D.source;else if(D.item||D.id){const v=D.item?D.item:r.query("GET_ITEM",D.id);G.file=v?de(v):null}return D.items&&(G.items=D.items.map(de)),/progress/.test(p)&&(G.progress=D.progress),D.hasOwnProperty("origin")&&D.hasOwnProperty("target")&&(G.origin=D.origin,G.target=D.target),G},P={DID_DESTROY:m("destroy"),DID_INIT:m("init"),DID_THROW_MAX_FILES:m("warning"),DID_INIT_ITEM:m("initfile"),DID_START_ITEM_LOAD:m("addfilestart"),DID_UPDATE_ITEM_LOAD_PROGRESS:m("addfileprogress"),DID_LOAD_ITEM:m("addfile"),DID_THROW_ITEM_INVALID:[m("error"),m("addfile")],DID_THROW_ITEM_LOAD_ERROR:[m("error"),m("addfile")],DID_THROW_ITEM_REMOVE_ERROR:[m("error"),m("removefile")],DID_PREPARE_OUTPUT:m("preparefile"),DID_START_ITEM_PROCESSING:m("processfilestart"),DID_UPDATE_ITEM_PROCESS_PROGRESS:m("processfileprogress"),DID_ABORT_ITEM_PROCESSING:m("processfileabort"),DID_COMPLETE_ITEM_PROCESSING:m("processfile"),DID_COMPLETE_ITEM_PROCESSING_ALL:m("processfiles"),DID_REVERT_ITEM_PROCESSING:m("processfilerevert"),DID_THROW_ITEM_PROCESSING_ERROR:[m("error"),m("processfile")],DID_REMOVE_ITEM:m("removefile"),DID_UPDATE_ITEMS:m("updatefiles"),DID_ACTIVATE_ITEM:m("activatefile"),DID_REORDER_ITEMS:m("reorderfiles")},S=p=>{const D={pond:V,...p};delete D.type,E.element.dispatchEvent(new CustomEvent(`FilePond:${p.type}`,{detail:D,bubbles:!0,cancelable:!0,composed:!0}));const G=[];p.hasOwnProperty("error")&&G.push(p.error),p.hasOwnProperty("file")&&G.push(p.file);const v=["type","error","file"];Object.keys(p).filter(k=>!v.includes(k)).forEach(k=>G.push(p[k])),V.fire(p.type,...G);const z=r.query(`GET_ON${p.type.toUpperCase()}`);z&&z(...G)},b=p=>{p.length&&p.filter(D=>P[D.type]).forEach(D=>{const G=P[D.type];(Array.isArray(G)?G:[G]).forEach(v=>{D.type==="DID_INIT_ITEM"?S(v(D.data)):setTimeout(()=>{S(v(D.data))},0)})})},C=p=>r.dispatch("SET_OPTIONS",{options:p}),M=p=>r.query("GET_ACTIVE_ITEM",p),w=p=>new Promise((D,G)=>{r.dispatch("REQUEST_ITEM_PREPARE",{query:p,success:v=>{D(v)},failure:v=>{G(v)}})}),F=(p,D={})=>new Promise((G,v)=>{B([{source:p,options:D}],{index:D.index}).then(z=>G(z&&z[0])).catch(v)}),W=p=>p.file&&p.id,A=(p,D)=>(typeof p=="object"&&!W(p)&&!D&&(D=p,p=void 0),r.dispatch("REMOVE_ITEM",{...D,query:p}),r.query("GET_ACTIVE_ITEM",p)===null),B=(...p)=>new Promise((D,G)=>{const v=[],z={};if(at(p[0]))v.push.apply(v,p[0]),Object.assign(z,p[1]||{});else{const k=p[p.length-1];typeof k=="object"&&!(k instanceof Blob)&&Object.assign(z,p.pop()),v.push(...p)}r.dispatch("ADD_ITEMS",{items:v,index:z.index,interactionMethod:Ie.API,success:D,failure:G})}),Y=()=>r.query("GET_ACTIVE_ITEMS"),N=p=>new Promise((D,G)=>{r.dispatch("REQUEST_ITEM_PROCESSING",{query:p,success:v=>{D(v)},failure:v=>{G(v)}})}),U=(...p)=>{const D=Array.isArray(p[0])?p[0]:p,G=D.length?D:Y();return Promise.all(G.map(w))},X=(...p)=>{const D=Array.isArray(p[0])?p[0]:p;if(!D.length){const G=Y().filter(v=>!(v.status===x.IDLE&&v.origin===se.LOCAL)&&v.status!==x.PROCESSING&&v.status!==x.PROCESSING_COMPLETE&&v.status!==x.PROCESSING_REVERT_ERROR);return Promise.all(G.map(N))}return Promise.all(D.map(N))},y=(...p)=>{const D=Array.isArray(p[0])?p[0]:p;let G;typeof D[D.length-1]=="object"?G=D.pop():Array.isArray(p[0])&&(G=p[1]);const v=Y();return D.length?D.map(k=>Pe(k)?v[k]?v[k].id:null:k).filter(k=>k).map(k=>A(k,G)):Promise.all(v.map(k=>A(k,G)))},V={...dt(),...f,...Ns(r,n),setOptions:C,addFile:F,addFiles:B,getFile:M,processFile:N,prepareFile:w,removeFile:A,moveFile:(p,D)=>r.dispatch("MOVE_ITEM",{query:p,index:D}),getFiles:Y,processFiles:X,removeFiles:y,prepareFiles:U,sort:p=>r.dispatch("SORT",{compare:p}),browse:()=>{var p=E.element.querySelector("input[type=file]");p&&p.click()},destroy:()=>{V.fire("destroy",E.element),r.dispatch("ABORT_ALL"),E._destroy(),window.removeEventListener("resize",c),document.removeEventListener("visibilitychange",s),r.dispatch("DID_DESTROY")},insertBefore:p=>un(E.element,p),insertAfter:p=>dn(E.element,p),appendTo:p=>p.appendChild(E.element),replaceElement:p=>{un(E.element,p),p.parentNode.removeChild(p),t=p},restoreElement:()=>{t&&(dn(t,E.element),E.element.parentNode.removeChild(E.element),t=null)},isAttachedTo:p=>E.element===p||t===p,element:{get:()=>E.element},status:{get:()=>r.query("GET_STATUS")}};return r.dispatch("DID_INIT"),Le(V)},Gr=(e={})=>{const t={};return Q(it(),(r,s)=>{t[r]=s[0]}),Cl({...t,...e})},vl=e=>e.charAt(0).toLowerCase()+e.slice(1),Nl=e=>vr(e.replace(/^data-/,"")),Fr=(e,t)=>{Q(t,(n,r)=>{Q(e,(s,o)=>{const i=new RegExp(n);if(!i.test(s)||(delete e[s],r===!1))return;if(ce(r)){e[r]=o;return}const l=r.group;ie(r)&&!e[l]&&(e[l]={}),e[l][vl(s.replace(i,""))]=o}),r.mapping&&Fr(e[r.group],r.mapping)})},Gl=(e,t={})=>{const n=[];Q(e.attributes,s=>{n.push(e.attributes[s])});const r=n.filter(s=>s.name).reduce((s,o)=>{const i=te(e,o.name);return s[Nl(o.name)]=i===o.name?!0:i,s},{});return Fr(r,t),r},Fl=(e,t={})=>{const n={"^class$":"className","^multiple$":"allowMultiple","^capture$":"captureMethod","^webkitdirectory$":"allowDirectoriesOnly","^server":{group:"server",mapping:{"^process":{group:"process"},"^revert":{group:"revert"},"^fetch":{group:"fetch"},"^restore":{group:"restore"},"^load":{group:"load"}}},"^type$":!1,"^files$":!1};Ge("SET_ATTRIBUTE_TO_OPTION_MAP",n);const r={...t},s=Gl(e.nodeName==="FIELDSET"?e.querySelector("input[type=file]"):e,n);Object.keys(s).forEach(i=>{ie(s[i])?(ie(r[i])||(r[i]={}),Object.assign(r[i],s[i])):r[i]=s[i]}),r.files=(t.files||[]).concat(Array.from(e.querySelectorAll("input:not([type=file])")).map(i=>({source:i.value,options:{type:i.dataset.type}})));const o=Gr(r);return e.files&&Array.from(e.files).forEach(i=>{o.addFile(i)}),o.replaceElement(e),o},Bl=(...e)=>kr(e[0])?Fl(...e):Gr(...e),Ul=["fire","_read","_write"],Hn=e=>{const t={};return rr(e,t,Ul),t},xl=(e,t)=>e.replace(/(?:{([a-zA-Z]+)})/g,(n,r)=>t[r]),Vl=e=>{const t=new Blob(["(",e.toString(),")()"],{type:"application/javascript"}),n=URL.createObjectURL(t),r=new Worker(n);return{transfer:(s,o)=>{},post:(s,o,i)=>{const a=Bt();r.onmessage=l=>{l.data.id===a&&o(l.data.message)},r.postMessage({id:a,message:s},i)},terminate:()=>{r.terminate(),URL.revokeObjectURL(n)}}},ql=e=>new Promise((t,n)=>{const r=new Image;r.onload=()=>{t(r)},r.onerror=s=>{n(s)},r.src=e}),Br=(e,t)=>{const n=e.slice(0,e.size,e.type);return n.lastModifiedDate=e.lastModifiedDate,n.name=t,n},Hl=e=>Br(e,e.name),Yn=[],Yl=e=>{if(Yn.includes(e))return;Yn.push(e);const t=e({addFilter:qs,utils:{Type:h,forin:Q,isString:ce,isFile:qe,toNaturalFileSize:Ir,replaceInString:xl,getExtensionFromFilename:ft,getFilenameWithoutExtension:Er,guesstimateMimeType:Lr,getFileFromBlob:Ve,getFilenameFromURL:je,createRoute:ue,createWorker:Vl,createView:ne,createItemAPI:de,loadImage:ql,copyFile:Hl,renameFile:Br,createBlob:ur,applyFilterChain:me,text:ee,getNumericAspectRatioFromString:or},views:{fileActionButton:_r}});Hs(t.options)},zl=()=>Object.prototype.toString.call(window.operamini)==="[object OperaMini]",$l=()=>"Promise"in window,Wl=()=>"slice"in Blob.prototype,kl=()=>"URL"in window&&"createObjectURL"in window.URL,Xl=()=>"visibilityState"in document,jl=()=>"performance"in window,Ql=()=>"supports"in(window.CSS||{}),Zl=()=>/MSIE|Trident/.test(window.navigator.userAgent),zn=(()=>{const e=Zn()&&!zl()&&Xl()&&$l()&&Wl()&&kl()&&jl()&&(Ql()||Zl());return()=>e})(),Ae={apps:[]},Kl="filepond",Fe=()=>{};let Nt={},$n={},rt=Fe,yt=Fe,Wn=Fe,kn=Fe,Gt=Fe,Xn=Fe,jn=Fe;if(zn()){gs(()=>{Ae.apps.forEach(n=>n._read())},n=>{Ae.apps.forEach(r=>r._write(n))});const e=()=>{document.dispatchEvent(new CustomEvent("FilePond:loaded",{detail:{supported:zn,create:rt,destroy:yt,parse:Wn,find:kn,registerPlugin:Gt,setOptions:jn}})),document.removeEventListener("DOMContentLoaded",e)};document.readyState!=="loading"?setTimeout(()=>e(),0):document.addEventListener("DOMContentLoaded",e);const t=()=>Q(it(),(n,r)=>{$n[n]=r[1]});Nt={...lr},$n={},t(),rt=(...n)=>{const r=Bl(...n);return r.on("destroy",yt),Ae.apps.push(r),Hn(r)},yt=n=>{const r=Ae.apps.findIndex(s=>s.isAttachedTo(n));return r>=0?(Ae.apps.splice(r,1)[0].restoreElement(),!0):!1},Wn=n=>Array.from(n.querySelectorAll(`.${Kl}`)).filter(o=>!Ae.apps.find(i=>i.isAttachedTo(o))).map(o=>rt(o)),kn=n=>{const r=Ae.apps.find(s=>s.isAttachedTo(n));return r?Hn(r):null},Gt=(...n)=>{n.forEach(Yl),t()},Xn=()=>{const n={};return Q(it(),(r,s)=>{n[r]=s[0]}),n},jn=n=>(ie(n)&&(Ae.apps.forEach(r=>{r.setOptions(n)}),Ys(n)),Xn())}/*!
 * FilePondPluginFileValidateSize 2.2.8
 * Licensed under MIT, https://opensource.org/licenses/MIT/
 * Please visit https://pqina.nl/filepond/ for details.
 */const Ur=({addFilter:e,utils:t})=>{const{Type:n,replaceInString:r,toNaturalFileSize:s}=t;return e("ALLOW_HOPPER_ITEM",(o,{query:i})=>{if(!i("GET_ALLOW_FILE_SIZE_VALIDATION"))return!0;const a=i("GET_MAX_FILE_SIZE");if(a!==null&&o.size>a)return!1;const l=i("GET_MIN_FILE_SIZE");return!(l!==null&&o.size<l)}),e("LOAD_FILE",(o,{query:i})=>new Promise((a,l)=>{if(!i("GET_ALLOW_FILE_SIZE_VALIDATION"))return a(o);const u=i("GET_FILE_VALIDATE_SIZE_FILTER");if(u&&!u(o))return a(o);const c=i("GET_MAX_FILE_SIZE");if(c!==null&&o.size>c){l({status:{main:i("GET_LABEL_MAX_FILE_SIZE_EXCEEDED"),sub:r(i("GET_LABEL_MAX_FILE_SIZE"),{filesize:s(c,".",i("GET_FILE_SIZE_BASE"),i("GET_FILE_SIZE_LABELS",i))})}});return}const E=i("GET_MIN_FILE_SIZE");if(E!==null&&o.size<E){l({status:{main:i("GET_LABEL_MIN_FILE_SIZE_EXCEEDED"),sub:r(i("GET_LABEL_MIN_FILE_SIZE"),{filesize:s(E,".",i("GET_FILE_SIZE_BASE"),i("GET_FILE_SIZE_LABELS",i))})}});return}const g=i("GET_MAX_TOTAL_FILE_SIZE");if(g!==null&&i("GET_ACTIVE_ITEMS").reduce((f,m)=>f+m.fileSize,0)>g){l({status:{main:i("GET_LABEL_MAX_TOTAL_FILE_SIZE_EXCEEDED"),sub:r(i("GET_LABEL_MAX_TOTAL_FILE_SIZE"),{filesize:s(g,".",i("GET_FILE_SIZE_BASE"),i("GET_FILE_SIZE_LABELS",i))})}});return}a(o)})),{options:{allowFileSizeValidation:[!0,n.BOOLEAN],maxFileSize:[null,n.INT],minFileSize:[null,n.INT],maxTotalFileSize:[null,n.INT],fileValidateSizeFilter:[null,n.FUNCTION],labelMinFileSizeExceeded:["File is too small",n.STRING],labelMinFileSize:["Minimum file size is {filesize}",n.STRING],labelMaxFileSizeExceeded:["File is too large",n.STRING],labelMaxFileSize:["Maximum file size is {filesize}",n.STRING],labelMaxTotalFileSizeExceeded:["Maximum total size exceeded",n.STRING],labelMaxTotalFileSize:["Maximum total file size is {filesize}",n.STRING]}}},Jl=typeof window<"u"&&typeof window.document<"u";Jl&&document.dispatchEvent(new CustomEvent("FilePond:pluginloaded",{detail:Ur}));/*!
 * FilePondPluginFileValidateType 1.2.8
 * Licensed under MIT, https://opensource.org/licenses/MIT/
 * Please visit https://pqina.nl/filepond/ for details.
 */const xr=({addFilter:e,utils:t})=>{const{Type:n,isString:r,replaceInString:s,guesstimateMimeType:o,getExtensionFromFilename:i,getFilenameFromURL:a}=t,l=(I,f)=>{const m=(/^[^/]+/.exec(I)||[]).pop(),P=f.slice(0,-2);return m===P},u=(I,f)=>I.some(m=>/\*$/.test(m)?l(f,m):m===f),c=I=>{let f="";if(r(I)){const m=a(I),P=i(m);P&&(f=o(P))}else f=I.type;return f},E=(I,f,m)=>{if(f.length===0)return!0;const P=c(I);return m?new Promise((S,b)=>{m(I,P).then(C=>{u(f,C)?S():b()}).catch(b)}):u(f,P)},g=I=>f=>I[f]===null?!1:I[f]||f;return e("SET_ATTRIBUTE_TO_OPTION_MAP",I=>Object.assign(I,{accept:"acceptedFileTypes"})),e("ALLOW_HOPPER_ITEM",(I,{query:f})=>f("GET_ALLOW_FILE_TYPE_VALIDATION")?E(I,f("GET_ACCEPTED_FILE_TYPES")):!0),e("LOAD_FILE",(I,{query:f})=>new Promise((m,P)=>{if(!f("GET_ALLOW_FILE_TYPE_VALIDATION")){m(I);return}const S=f("GET_ACCEPTED_FILE_TYPES"),b=f("GET_FILE_VALIDATE_TYPE_DETECT_TYPE"),C=E(I,S,b),M=()=>{const w=S.map(g(f("GET_FILE_VALIDATE_TYPE_LABEL_EXPECTED_TYPES_MAP"))).filter(W=>W!==!1),F=w.filter(function(W,A){return w.indexOf(W)===A});P({status:{main:f("GET_LABEL_FILE_TYPE_NOT_ALLOWED"),sub:s(f("GET_FILE_VALIDATE_TYPE_LABEL_EXPECTED_TYPES"),{allTypes:F.join(", "),allButLastType:F.slice(0,-1).join(", "),lastType:F[w.length-1]})}})};if(typeof C=="boolean")return C?m(I):M();C.then(()=>{m(I)}).catch(M)})),{options:{allowFileTypeValidation:[!0,n.BOOLEAN],acceptedFileTypes:[[],n.ARRAY],labelFileTypeNotAllowed:["File is of invalid type",n.STRING],fileValidateTypeLabelExpectedTypes:["Expects {allButLastType} or {lastType}",n.STRING],fileValidateTypeLabelExpectedTypesMap:[{},n.OBJECT],fileValidateTypeDetectType:[null,n.FUNCTION]}}},ea=typeof window<"u"&&typeof window.document<"u";ea&&document.dispatchEvent(new CustomEvent("FilePond:pluginloaded",{detail:xr}));function ta(){Gt(Ur,xr);const e=this,t=this.$wire;return t.on("scrollResultIntoView",()=>this.$refs.result.scrollIntoView({behavior:"instant",block:"center"})),{isConvertBtnDisabled:!0,showConvertBtn:!1,showPlaceHolder:!0,showUploader:!0,filepond:rt(e.$refs.filepond,{credits:!1,server:{process:{url:t.routes.upload,onerror:n=>n},revert:(n,r,s)=>{t.delete(n),r()},headers:{"X-CSRF-TOKEN":document.querySelector('meta[name="csrf-token"]').content}},labelFileProcessingError:n=>n.code===0?t.locales.networkError:n.body,name:"image",maxFileSize:t.maxFileSize,allowMultiple:!0,maxFiles:t.maxFiles,maxParallelUploads:t.maxFiles,labelInvalidField:t.locales.labelInvalidField,labelFileWaitingForSize:t.locales.labelFileWaitingForSize,labelFileSizeNotAvailable:t.locales.labelFileSizeNotAvailable,labelFileLoading:t.locales.labelFileLoading,labelFileLoadError:t.locales.labelFileLoadError,labelFileProcessing:t.locales.labelFileProcessing,labelFileProcessingComplete:t.locales.labelFileProcessingComplete,labelFileProcessingAborted:t.locales.labelFileProcessingAborted,labelFileProcessingRevertError:t.locales.labelFileProcessingRevertError,labelFileRemoveError:t.locales.labelFileRemoveError,labelTapToCancel:t.locales.labelTapToCancel,labelTapToRetry:t.locales.labelTapToRetry,labelTapToUndo:t.locales.labelTapToUndo,labelButtonRemoveItem:t.locales.labelButtonRemoveItem,labelButtonAbortItemLoad:t.locales.labelButtonAbortItemLoad,labelButtonRetryItemLoad:t.locales.labelButtonRetryItemLoad,labelButtonAbortItemProcessing:t.locales.labelButtonAbortItemProcessing,labelButtonUndoItemProcessing:t.locales.labelButtonUndoItemProcessing,labelButtonRetryItemProcessing:t.locales.labelButtonRetryItemProcessing,labelButtonProcessItem:t.locales.labelButtonProcessItem,labelFileTypeNotAllowed:t.locales.labelFileTypeNotAllowed,fileValidateTypeLabelExpectedTypes:t.locales.fileValidateTypeLabelExpectedTypes,labelMaxFileSizeExceeded:t.locales.labelMaxFileSizeExceeded,labelMaxFileSize:t.locales.labelMaxFileSize,labelIdle:`<div class="flex flex-col items-center justify-center w-full text-uploader-text-color p-3">
        <svg class="upload-icon text-uploader-text-color opacity-50 h-24 w-24 mb-3" fill="none" stroke="currentColor" stroke-width="1.25" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 16.5V9.75m0 0l3 3m-3-3l-3 3M6.75 19.5a4.5 4.5 0 01-1.41-8.775 5.25 5.25 0 0110.233-2.33 3 3 0 013.758 3.848A3.752 3.752 0 0118 19.5H6.75z"></path>
        </svg>
        <button @click="browse" class="browse-btn btn bg-browse-btn-bg-color text-browse-btn-text-color opacity-95 hover:bg-browse-btn-bg-color hover:opacity-100 font-medium px-3 mb-3">
        <svg class="h-7 w-7 mb-1" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 9.776c.112-.017.227-.026.344-.026h15.812c.117 0 .232.009.344.026m-16.5 0a2.25 2.25 0 00-1.883 2.542l.857 6a2.25 2.25 0 002.227 1.932H19.05a2.25 2.25 0 002.227-1.932l.857-6a2.25 2.25 0 00-1.883-2.542m-16.5 0V6A2.25 2.25 0 016 3.75h3.879a1.5 1.5 0 011.06.44l2.122 2.12a1.5 1.5 0 001.06.44H18A2.25 2.25 0 0120.25 9v.776"></path>
        </svg>
          <span>${t.locales.browseFiles}</span>
        </button>
        <p class="">${t.locales.dragNDrop}</p>
      </div>`}),init(){this.filepond.on("init",()=>{this.showPlaceHolder=!1,this.showConvertBtn=!0}),this.filepond.on("addfile",(n,r)=>{this.isConvertBtnDisabled=!0}),this.filepond.on("processfile",()=>{this.filepond.status===Nt.READY&&(this.isConvertBtnDisabled=!1)}),this.filepond.on("removefile",()=>{this.filepond.status===Nt.READY?this.isConvertBtnDisabled=!1:this.isConvertBtnDisabled=!0}),this.filepond.on("warning",n=>{n.body==="Max files"&&new FilamentNotification().title(t.locales.maxUploadExceeded).warning().duration(6e3).send()})},browse(){this.filepond.browse()},async convert(){if(navigator.onLine){const n=this.filepond.getFiles().map(r=>({id:r.serverId,basename:r.filenameWithoutExtension,originalName:r.filename}));await t.convert(n)}}}}qr.data("filepond",ta);Hr.start();document.addEventListener("DOMContentLoaded",()=>{document.querySelectorAll(".social-button").forEach(t=>{t.addEventListener("click",na)})});function na(e){let r=ra(e,sa());if(r===void 0)return;if(r.id==="clip"){if(e.preventDefault(),e.stopImmediatePropagation(),window.clipboardData&&window.clipboardData.setData)clipboardData.setData("Text",r.href);else{let u=document.createElement("textarea");u.value=r.href,document.body.appendChild(u),u.select(),document.execCommand("copy"),u.remove()}return}const s=window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth,o=window.innerHeight||document.documentElement.clientHeight||document.body.clientHeight,i=Math.floor((s-780)/2),a=Math.floor((o-550)/2),l=window.open(r.href,"social","width=780,height=550,left="+i+",top="+a+",location=0,menubar=0,toolbar=0,status=0,scrollbars=1,resizable=1");l&&(e.preventDefault(),e.stopImmediatePropagation(),l.focus())}function ra(e,t){let n="social-button";if(e.target.parentElement&&e.target.parentElement.className.indexOf(n)!==-1)return e.target.parentElement;if(e.target.className.indexOf(n)!==-1)return e.target;typeof t=="function"&&t(n)}function sa(e){return t=>{}}
