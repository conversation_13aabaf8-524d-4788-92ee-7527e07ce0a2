<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;

class SitemapController extends Controller
{
    /**
     * Generate XML sitemap for SEO
     */
    public function index(Request $request): Response
    {
        $baseUrl = $request->getSchemeAndHttpHost();
        $converters = converters();
        
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml">' . "\n";
        
        // Home page
        $xml .= $this->addUrl($baseUrl, '/', '1.0', 'daily', now()->toISOString());
        
        // Converter pages
        foreach ($converters as $converter) {
            if ($converter['enabled']) {
                $url = '/' . $converter['name'];
                $xml .= $this->addUrl($baseUrl, $url, '0.9', 'weekly', now()->toISOString());
            }
        }
        
        // Static pages (if any)
        $staticPages = [
            '/about' => '0.7',
            '/privacy' => '0.5',
            '/terms' => '0.5',
            '/contact' => '0.6'
        ];
        
        foreach ($staticPages as $page => $priority) {
            $xml .= $this->addUrl($baseUrl, $page, $priority, 'monthly', now()->toISOString());
        }
        
        $xml .= '</urlset>';
        
        return response($xml, 200, [
            'Content-Type' => 'application/xml',
            'Cache-Control' => 'public, max-age=3600'
        ]);
    }
    
    /**
     * Add URL to sitemap
     */
    private function addUrl(string $baseUrl, string $path, string $priority, string $changefreq, string $lastmod): string
    {
        $url = rtrim($baseUrl, '/') . $path;
        
        return "  <url>\n" .
               "    <loc>{$url}</loc>\n" .
               "    <lastmod>{$lastmod}</lastmod>\n" .
               "    <changefreq>{$changefreq}</changefreq>\n" .
               "    <priority>{$priority}</priority>\n" .
               "  </url>\n";
    }
    
    /**
     * Generate robots.txt
     */
    public function robots(Request $request): Response
    {
        $baseUrl = $request->getSchemeAndHttpHost();
        
        $robots = "User-agent: *\n";
        $robots .= "Allow: /\n";
        $robots .= "Disallow: /admin/\n";
        $robots .= "Disallow: /storage/uploads/\n";
        $robots .= "Disallow: /storage/converted/\n";
        $robots .= "\n";
        $robots .= "Sitemap: {$baseUrl}/sitemap.xml\n";
        
        return response($robots, 200, [
            'Content-Type' => 'text/plain',
            'Cache-Control' => 'public, max-age=86400'
        ]);
    }
}
