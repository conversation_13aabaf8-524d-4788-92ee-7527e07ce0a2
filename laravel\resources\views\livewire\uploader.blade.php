<section x-data="filepond" class="uploader" x-ref="result">
    <!-- Loading Skeleton -->
    <div x-show="showPlaceHolder" class="space-y-4" x-cloak>
        <div class="w-full h-64 skeleton rounded-2xl"></div>
        <div class="h-12 rounded-xl skeleton w-32"></div>
    </div>

    @if ($showUploader)
        <div x-show='showUploader' class="space-y-6">
            <!-- Upload Instructions -->
            <div class="text-center mb-6">
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Upload Your Images</h3>
                <p class="text-gray-600">
                    Drag and drop your images here or click to browse.
                    <span class="text-sm text-gray-500">Max {{ $maxFileSize }}, up to {{ $maxFiles }} files</span>
                </p>
            </div>

            <!-- File Upload Area -->
            <div wire:ignore class="relative">
                <div class="filepond" x-ref='filepond'></div>
            </div>

            <!-- Convert Button -->
            <div class="text-center">
                <button x-cloak x-show="showConvertBtn" x-bind:disabled="isConvertBtnDisabled" @click="convert"
                    class="inline-flex items-center px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none shadow-lg hover:shadow-xl">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    {{ __('Convert Images') }}
                </button>
            </div>
        </div>
    @endif
    <!-- Loading Overlay -->
    <div class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm"
        wire:loading.flex wire:target="convert" x-cloak>
        <div class="bg-white rounded-2xl p-8 shadow-2xl text-center max-w-sm mx-4">
            <div class="w-16 h-16 mx-auto mb-4">
                <svg class="w-16 h-16 animate-spin text-blue-600" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Converting Images...</h3>
            <p class="text-gray-600">Please wait while we process your images</p>
        </div>
    </div>

    <!-- Error Messages -->
    @if ($errors->any())
        <div class="space-y-3 mb-6">
            @foreach ($errors->all() as $error)
                <div class="flex items-start gap-3 p-4 bg-red-50 border border-red-200 rounded-xl">
                    <div class="flex-shrink-0">
                        <svg class="w-5 h-5 text-red-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <p class="text-red-800 font-medium">{{ $error }}</p>
                    </div>
                </div>
            @endforeach

            @if (empty($results))
                <div class="text-center mt-6">
                    <a class="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-xl hover:bg-blue-700 transition-colors duration-300"
                        href="{{ localizedRoute('resolver', $slug) }}">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('Try Other Images') }}
                    </a>
                </div>
            @endif
        </div>
    @endif
    <!-- Results Section -->
    @if (!empty($results))
        <div class="bg-gradient-to-br from-green-50 to-emerald-100 rounded-2xl p-6 border border-green-200">
            <!-- Success Header -->
            <div class="text-center mb-6">
                <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold text-green-800 mb-2">Conversion Complete!</h3>
                <p class="text-green-700">Your images have been successfully converted to {{ strtoupper($format) }} format.</p>
            </div>

            <!-- Results List -->
            <div class="space-y-3 mb-6">
                @foreach ($results['images'] as $result)
                    @php
                        $id = $result['id'];
                        $basename = $result['basename'];
                        $size = $result['size'];
                    @endphp
                    <div class="bg-white rounded-xl p-4 shadow-sm border border-green-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center flex-1 min-w-0">
                                <div class="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-semibold text-gray-900 truncate">
                                        {{ "{$basename}.{$format}" }}
                                    </p>
                                    <p class="text-xs text-gray-500">{{ $size }}</p>
                                </div>
                            </div>

                            <a href="{{ localizedRoute('download') }}/?id={{ $id }}&basename={{ $basename }}&format={{ $format }}"
                                class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors duration-300 ml-4"
                                target="_blank">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                                {{ __('Download') }}
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>
            <!-- Action Buttons -->
            <div class="flex flex-wrap justify-center gap-4 pt-4 border-t border-green-200">
                <a class="inline-flex items-center px-6 py-3 bg-white text-gray-700 font-medium rounded-xl border border-gray-300 hover:bg-gray-50 transition-colors duration-300"
                    href="{{ localizedRoute('resolver', $slug) }}">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
                    </svg>
                    {{ __('Convert More Images') }}
                </a>

                <button onclick="deleteModal.showModal()"
                    class="inline-flex items-center px-4 py-3 bg-red-50 text-red-600 font-medium rounded-xl border border-red-200 hover:bg-red-100 transition-colors duration-300">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    {{ __('Delete All') }}
                </button>

                @if ($results['zipId'] == null)
                    <button disabled
                        class="inline-flex items-center px-6 py-3 bg-gray-100 text-gray-400 font-medium rounded-xl cursor-not-allowed">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('ZIP Unavailable') }}
                    </button>
                @else
                    <a href="{{ localizedRoute('downloadZip') }}/?zipId={{ $results['zipId'] }}"
                        class="inline-flex items-center px-6 py-3 bg-green-600 text-white font-medium rounded-xl hover:bg-green-700 transition-colors duration-300 shadow-lg hover:shadow-xl"
                        target="_blank">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('Download All as ZIP') }}
                    </a>
                @endif
            </div>

            <!-- Delete Confirmation Modal -->
            <dialog id="deleteModal" class="modal">
                <div class="modal-box bg-white rounded-2xl shadow-2xl max-w-md">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-2">{{ __('Confirm Deletion') }}</h3>
                        <p class="text-gray-600 mb-6">{{ __('Are you sure you want to delete all images from the server? This action cannot be undone.') }}</p>

                        <div class="flex gap-3 justify-center">
                            <form method="dialog">
                                <button class="px-6 py-2 bg-gray-100 text-gray-700 font-medium rounded-lg hover:bg-gray-200 transition-colors duration-300">
                                    {{ __('Cancel') }}
                                </button>
                            </form>
                            <a wire:click="deleteAll"
                                class="px-6 py-2 bg-red-600 text-white font-medium rounded-lg hover:bg-red-700 transition-colors duration-300"
                                href="{{ localizedRoute('resolver', $slug) }}">
                                {{ __('Delete All') }}
                            </a>
                        </div>
                    </div>
                </div>
            </dialog>
        </div>
    @endif
</section>
