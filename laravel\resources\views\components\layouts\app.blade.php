<!DOCTYPE html>
<html lang="{{ locale() }}" class="scroll-smooth">
<x-head :noIndex="$noIndex ?? false" />

<body class="flex flex-col min-h-screen antialiased bg-bg-color text-text-color">
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-lg z-50 transition-all duration-300">
        {{ __('Skip to main content') }}
    </a>

    <x-no-script />
    <x-navbar />
    @if ($sidebarSettings->enableSidebar)
        <div class="container grid grid-rows-[auto_1fr] lg:grid-rows-[auto] min-h-screen gap-8 lg:grid-cols-3 mb-8 flex-1">
            <main id="main-content" class="flex flex-col lg:col-span-2 fade-in">
                <div class="space-y-8">
                    {{ $slot }}
                </div>
            </main>
            <x-sidebar />
        </div>
    @else
        <div class="container flex-1 mb-8 max-w-7xl mx-auto px-4">
            <main id="main-content" class="flex flex-col fade-in">
                <div class="space-y-8">
                    {{ $slot }}
                </div>
            </main>
        </div>
    @endif
    <!-- Footer -->
    <x-footer />

    <!-- Back to Top Button -->
    <x-to-up />

    <!-- Offline Alert -->
    <livewire:alert-offline />

    <!-- Notifications -->
    @livewire('notifications')

    <!-- Livewire Scripts -->
    @livewireScriptConfig

    <!-- Cookie Consent -->
    @include('cookie-consent::index')

    <!-- Performance Enhancements Script -->
    <script>
        // Critical performance optimizations
        document.addEventListener('DOMContentLoaded', function() {
            // Add loading states
            document.body.classList.add('loaded');

            // Optimize images
            const images = document.querySelectorAll('img');
            images.forEach(img => {
                if (!img.hasAttribute('loading')) {
                    img.setAttribute('loading', 'lazy');
                }
            });

            // Add smooth scrolling to anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
    </script>

    <!-- Analytics (Production Only) -->
    @if(config('app.env') === 'production')
        <!-- Add your analytics code here -->
    @endif
</body>

</html>
