/* Enhanced Styles for Image Converter */

/* Performance Optimizations */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Loading Animations */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 0.5rem;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Smooth Transitions */
.transition-all {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-colors {
    transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

.transition-transform {
    transition: transform 0.3s ease;
}

/* Enhanced <PERSON>tons */
.btn-enhanced {
    position: relative;
    overflow: hidden;
    transform: translateZ(0);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.btn-enhanced:active {
    transform: translateY(0);
}

/* Card Hover Effects */
.card-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Gradient Backgrounds */
.gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-blue {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-green {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* File Upload Enhancements */
.filepond--root {
    border-radius: 1rem;
    border: 2px dashed #e5e7eb;
    transition: all 0.3s ease;
}

.filepond--root:hover {
    border-color: #3b82f6;
    background-color: #f8fafc;
}

.filepond--panel-root {
    border-radius: 1rem;
    background-color: #f8fafc;
}

.filepond--drop-label {
    color: #6b7280;
    font-weight: 500;
}

/* Progress Bars */
.progress-bar {
    height: 8px;
    background-color: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* Modal Enhancements */
.modal-backdrop {
    backdrop-filter: blur(8px);
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Responsive Images */
.responsive-img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
}

/* Focus States */
.focus-ring:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Text Animations */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Utility Classes */
.text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.backdrop-blur {
    backdrop-filter: blur(10px);
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .dark-mode-auto {
        background-color: #1f2937;
        color: #f9fafb;
    }
    
    .dark-mode-auto .skeleton {
        background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
        background-size: 200% 100%;
    }
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .btn-enhanced {
        border: 2px solid currentColor;
    }
    
    .card-hover {
        border: 1px solid currentColor;
    }
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .mobile-optimized {
        padding: 1rem;
    }
    
    .btn-enhanced {
        min-height: 44px; /* Touch target size */
        min-width: 44px;
    }
}

/* Performance: GPU Acceleration */
.gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
}

/* Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Enhanced File Upload Styles */
.filepond--root {
    font-family: var(--font-family);
}

.filepond--panel-root {
    background-color: #f8fafc;
    border: 2px dashed #e2e8f0;
    border-radius: 1rem;
    transition: all 0.3s ease;
}

.filepond--panel-root:hover {
    border-color: #3b82f6;
    background-color: #eff6ff;
}

.filepond--drop-label {
    color: #64748b;
    font-size: 1rem;
    font-weight: 500;
}

.filepond--label-action {
    color: #3b82f6;
    font-weight: 600;
    text-decoration: underline;
}

/* Enhanced Button Styles */
.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: none;
    color: white;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -3px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
    background: white;
    border: 2px solid #e2e8f0;
    color: #374151;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    border-color: #3b82f6;
    color: #3b82f6;
    transform: translateY(-1px);
}

/* Loading States */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success States */
.success-message {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.3);
}

/* Error States */
.error-message {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(239, 68, 68, 0.3);
}

/* Enhanced Card Styles */
.card-enhanced {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.card-enhanced:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-color: #3b82f6;
}

/* Responsive Grid Enhancements */
.grid-responsive {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

@media (max-width: 640px) {
    .grid-responsive {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}
